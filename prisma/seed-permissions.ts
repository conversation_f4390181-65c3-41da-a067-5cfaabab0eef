import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedPermissions() {
  console.log('Seeding permissions...');

  // Create system roles that map to UserType
  console.log('Creating system roles...');

  const systemRoles = [
    {
      name: 'Super Admin',
      description: 'Full system access - maps to ADMIN UserType',
      isSystemRole: true,
    },
    {
      name: 'Warehouse Staff',
      description:
        'Limited access for warehouse operations - maps to WAREHOUSE_STAFF UserType',
      isSystemRole: true,
    },
    {
      name: 'Delivery Driver',
      description:
        'Access for delivery operations - maps to DELIVERY_PERSON UserType',
      isSystemRole: true,
    },
  ];

  for (const role of systemRoles) {
    await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role,
    });
  }

  console.log('System roles created successfully!');
}

seedPermissions()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
