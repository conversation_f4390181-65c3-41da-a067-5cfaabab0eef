/*
  Warnings:

  - The primary key for the `RolePermission` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `featureId` on the `RolePermission` table. All the data in the column will be lost.
  - You are about to drop the `PermissionFeature` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `feature` to the `RolePermission` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "PermissionFeature" AS ENUM ('ORDER', 'PRODUCT', 'USER', 'INVENTORY', 'ANALYTICS', 'WAREHOUSE', 'COUPON', 'NOTIFICATION');

-- DropForeign<PERSON>ey
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_featureId_fkey";

-- AlterTable
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_pkey",
DROP COLUMN "featureId",
ADD COLUMN     "feature" "PermissionFeature" NOT NULL,
ADD CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("roleId", "feature", "accessLevel");

-- DropTable
DROP TABLE "PermissionFeature";
