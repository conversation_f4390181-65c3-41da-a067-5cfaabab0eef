/*
  Warnings:

  - Drop and recreate permission-related tables to convert PermissionFeature from model to enum
  - This is safe for production since no permissions have been created yet

*/

-- Drop dependent tables first
DROP TABLE IF EXISTS "RolePermission";
DROP TABLE IF EXISTS "UserRole";
DROP TABLE IF EXISTS "Role";
DROP TABLE IF EXISTS "PermissionFeature";

-- CreateEnum
CREATE TYPE "PermissionFeature" AS ENUM ('ORDER', 'PRODUCT', 'USER', 'INVENTORY', 'ANALYTICS', 'WAREHOUSE', 'COUPON', 'NOTIFICATION');

-- CreateTable
CREATE TABLE "Role" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isSystemRole" BOOLEAN NOT NULL DEFAULT false,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RolePermission" (
    "roleId" INTEGER NOT NULL,
    "feature" "PermissionFeature" NOT NULL,
    "accessLevel" "AccessLevel" NOT NULL,

    CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("roleId", "feature", "accessLevel")
);

-- CreateTable
CREATE TABLE "UserRole" (
    "userId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserRole_pkey" PRIMARY KEY ("userId", "roleId")
);

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- AddForeignKey
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;
