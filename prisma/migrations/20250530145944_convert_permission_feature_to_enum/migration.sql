/*
  Warnings:

  - The primary key for the `RolePermission` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `featureId` on the `RolePermission` table. All the data in the column will be lost.
  - You are about to drop the `PermissionFeature` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `feature` to the `RolePermission` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "PermissionFeature" AS ENUM ('ORDER', 'PRODUCT', 'USER', 'INVENTORY', 'ANALYTICS', 'WAREHOUSE', 'COUPON', 'NOTIFICATION');

-- Add temporary feature column
ALTER TABLE "RolePermission" ADD COLUMN "feature" "PermissionFeature";

-- Update feature column based on featureId and PermissionFeature name mapping
UPDATE "RolePermission"
SET "feature" = CASE
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Order') THEN 'ORDER'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Product') THEN 'PRODUCT'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'User') THEN 'USER'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Inventory') THEN 'INVENTORY'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Analytics') THEN 'ANALYTICS'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Warehouse') THEN 'WAREHOUSE'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Coupon') THEN 'COUPON'::PermissionFeature
  WHEN "featureId" IN (SELECT id FROM "PermissionFeature" WHERE name = 'Notification') THEN 'NOTIFICATION'::PermissionFeature
  ELSE 'ORDER'::PermissionFeature -- Default fallback
END;

-- Make feature column NOT NULL
ALTER TABLE "RolePermission" ALTER COLUMN "feature" SET NOT NULL;

-- Drop old constraints and foreign key
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_pkey";
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_featureId_fkey";

-- Drop old column
ALTER TABLE "RolePermission" DROP COLUMN "featureId";

-- Add new primary key
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("roleId", "feature", "accessLevel");

-- Drop PermissionFeature table
DROP TABLE "PermissionFeature";
