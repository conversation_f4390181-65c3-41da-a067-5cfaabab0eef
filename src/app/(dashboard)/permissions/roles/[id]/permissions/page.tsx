"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowLeft, Save } from "lucide-react";
import { toast } from "sonner";
import {
  Role,
  PermissionFeature,
  RolePermission,
  AccessLevel,
  PERMISSION_FEATURES,
} from "@/frontend-types";
import { apiService } from "@/api";

const ACCESS_LEVELS: AccessLevel[] = ["VIEW", "MANAGE", "DELETE"];

function getAccessLevelColor(level: AccessLevel): string {
  switch (level) {
    case "VIEW":
      return "text-blue-600";
    case "MANAGE":
      return "text-orange-600";
    case "DELETE":
      return "text-red-600";
    default:
      return "text-gray-600";
  }
}

function getAccessLevelDescription(level: AccessLevel): string {
  switch (level) {
    case "VIEW":
      return "Read access";
    case "MANAGE":
      return "Create and update";
    case "DELETE":
      return "Delete access";
    default:
      return "";
  }
}

function getFeatureDescription(feature: PermissionFeature): string {
  switch (feature) {
    case "ORDER":
      return "Manage orders, view order details, update order status";
    case "PRODUCT":
      return "Manage products";
    case "USER":
      return "Manage users, assign roles, view user details";
    case "INVENTORY":
      return "Manage inventory";
    case "ANALYTICS":
      return "View analytics, reports, and dashboard data";
    case "WAREHOUSE":
      return "Manage warehouses";
    case "COUPON":
      return "Manage coupons";
    case "NOTIFICATION":
      return "Send notifications";
    default:
      return "";
  }
}

export default function RolePermissionsPage() {
  const params = useParams();
  const router = useRouter();
  const roleId = parseInt(params.id as string);

  const [role, setRole] = useState<Role | null>(null);
  const [permissions, setPermissions] = useState<Map<string, boolean>>(
    new Map()
  );
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadData();
  }, [roleId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const roleResponse = await apiService.get(`admin/roles/${roleId}`);

      setRole(roleResponse.data);

      // Initialize permissions map
      const permissionsMap = new Map<string, boolean>();
      roleResponse.data.permissions?.forEach((permission: RolePermission) => {
        const key = `${permission.feature}-${permission.accessLevel}`;
        permissionsMap.set(key, true);
      });
      setPermissions(permissionsMap);
    } catch (error) {
      console.error("Failed to load data:", error);
      toast.error("Failed to load role permissions");
    } finally {
      setLoading(false);
    }
  };

  const getPermissionKey = (
    feature: PermissionFeature,
    accessLevel: AccessLevel
  ): string => {
    return `${feature}-${accessLevel}`;
  };

  const getPermissionState = (
    feature: PermissionFeature,
    accessLevel: AccessLevel
  ): boolean => {
    return permissions.get(getPermissionKey(feature, accessLevel)) || false;
  };

  const handlePermissionChange = (
    feature: PermissionFeature,
    accessLevel: AccessLevel,
    checked: boolean
  ) => {
    const key = getPermissionKey(feature, accessLevel);
    const newPermissions = new Map(permissions);

    if (checked) {
      newPermissions.set(key, true);
    } else {
      newPermissions.delete(key);
    }

    setPermissions(newPermissions);
  };

  const handleSave = async () => {
    if (!role) return;

    try {
      setSaving(true);

      // Convert permissions map to array format
      const permissionsArray: Array<{
        feature: PermissionFeature;
        accessLevel: AccessLevel;
      }> = [];
      permissions.forEach((_, key) => {
        const [feature, accessLevel] = key.split("-");
        permissionsArray.push({
          feature: feature as PermissionFeature,
          accessLevel: accessLevel as AccessLevel,
        });
      });

      await apiService.post(`admin/roles/${roleId}/permissions`, {
        roleId: role.id,
        permissions: permissionsArray,
      });

      toast.success("Permissions updated successfully");
      router.back();
    } catch (error) {
      console.error("Failed to save permissions:", error);
      toast.error("Failed to save permissions");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!role) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Role not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              Manage {role.name} Permissions
            </h1>
          </div>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? "Saving..." : "Save Permissions"}
        </Button>
      </div>

      {/* Permissions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/2">Feature</TableHead>
                  {ACCESS_LEVELS.map((level) => (
                    <TableHead key={level} className="text-center w-32">
                      <div
                        className={`font-medium ${getAccessLevelColor(level)}`}
                      >
                        {level}
                      </div>
                      <div className="text-xs text-gray-500 font-normal mt-1">
                        {getAccessLevelDescription(level)}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {PERMISSION_FEATURES.map((feature) => (
                  <TableRow key={feature} className="hover:bg-gray-50">
                    <TableCell className="font-medium py-4">
                      <div className="space-y-1">
                        <div className="font-semibold">{feature}</div>
                        <div className="text-sm text-gray-500 leading-relaxed">
                          {getFeatureDescription(feature)}
                        </div>
                      </div>
                    </TableCell>
                    {ACCESS_LEVELS.map((accessLevel) => (
                      <TableCell key={accessLevel} className="text-center py-4">
                        <div className="flex justify-center">
                          <Checkbox
                            checked={getPermissionState(feature, accessLevel)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange(
                                feature,
                                accessLevel,
                                checked as boolean
                              )
                            }
                          />
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
