import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { Admin } from 'src/auth/auth.decorator';
import { UpdateUserDto } from './users.schema';
import { UsersService } from './users.service';
import { PermissionGuard } from 'src/auth/permission.guard';
import { CanManage, CanView } from 'src/auth/permission.decorator';

@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @CanView('USER')
  @Get(':id/warehouses')
  async getUserWarehouses(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getUserWarehouses(id);
  }

  @CanView('USER')
  @Get()
  async getUsers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe)
    pageSize: number,
    @Query('search') search?: string,
  ) {
    return this.usersService.getUsers({
      page: page,
      pageSize: pageSize,
      search: search,
    });
  }

  @CanView('USER')
  @Get(':id')
  async getUser(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getUser({
      id: id,
    });
  }

  @CanManage('USER')
  @Patch('/:id')
  async updateUser(
    @Body() updateUserDto: UpdateUserDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @CanManage('USER')
  @Get('download/excel')
  async downloadUsersExcel(@Res({ passthrough: true }) res: Response) {
    const streamableFile = await this.usersService.downloadUsersExcel();

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `users-export-${timestamp}.xlsx`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return streamableFile;
  }
}
