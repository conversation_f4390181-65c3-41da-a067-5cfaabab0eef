import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpdateOrderStatusDto, UpdateOrderDeliveryDto } from './orders.schema';
import { InventoryService } from '../inventories/inventories.service';
import { NotificationService } from '../notification/notification.service';
import { RewardTiersService } from 'src/admin/reward-tiers/reward-tiers.service';
import { AdminOrderTransformer } from 'src/transformers/admin-order.transformer';
import { AdminOrderResponse, OrderResponse } from 'src/types';
import { EmailService } from 'src/email/email.service';

@Injectable()
export class OrdersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly inventoryService: InventoryService,
    private readonly notificationService: NotificationService,
    private readonly rewardTiersService: RewardTiersService,
    private readonly adminOrderTransformer: AdminOrderTransformer,
    private readonly emailService: EmailService,
  ) {}

  async getOrders(params: { page?: number; pageSize?: number }) {
    const { page = 1, pageSize = 10 } = params;

    const [orders, total] = await this.prisma.$transaction([
      this.prisma.order.findMany({
        skip: (page - 1) * pageSize,
        take: params.pageSize,
        include: {
          items: {
            include: {
              product: {
                include: {
                  thumbnail: true,
                },
              },
              inventory: true,
              variation: {
                include: {
                  options: {
                    include: {
                      option: {
                        include: {
                          attribute: true,
                        },
                      },
                    },
                  },
                  media: true,
                },
              },
            },
          },
          address: true,
          user: true,
          orderStatusHistory: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.order.count({}),
    ]);

    return {
      data: orders,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getOrderById(id: number): Promise<AdminOrderResponse> {
    const order = await this.prisma.order.findUniqueOrThrow({
      where: { id },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
                thumbnail: true,
              },
            },
            variation: {
              include: {
                options: {
                  include: {
                    option: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
                media: true,
              },
            },
            inventory: {
              include: {
                inventoryTransactions: true,
                batch: {
                  include: {
                    warehouse: true,
                  },
                },
              },
            },
          },
        },
        address: {
          include: {
            country: true,
          },
        },
        user: {
          select: {
            id: true,
            createdAt: true,
            updatedAt: true,
            firebaseToken: true,
            name: true,
            phone: true,
            countryId: true,
            phoneCountry: true,
            profilePicture: true,
            rewardTierId: true,
            referralCode: true,
            referredById: true,
            email: true,
            type: true,
            orders: true,
            tierExpiresAt: true,
          },
        },
        orderStatusHistory: true,
        deliveryDriver: {
          include: {
            phoneCountry: true,
          },
        },
        warehouse: true,
      },
    });

    const totalOrders = order.user.orders.length;
    const totalOrderPrice = order.user.orders.reduce(
      (acc, curr) => acc + +curr.totalAmount,
      0,
    );

    const averageOrderprice = totalOrderPrice / totalOrders;

    // Process the order with inventory quantity
    const processedOrder = {
      ...order,
      user: {
        ...order.user,
        totalOrders: totalOrders,
        totalOrderPrice: totalOrderPrice.toFixed(2),
        averageOrderPrice: averageOrderprice.toFixed(2),
      },
      items: order.items.map((item) => ({
        ...item,
        inventory: {
          ...item.inventory,
          quantity: this.inventoryService.getQuantityOfInventoryItem(
            item.inventory,
          ),
        },
      })),
    };

    // Transform the order using the admin order transformer
    return await this.adminOrderTransformer.getAdminOrderResponse(
      processedOrder,
    );
  }

  async updateOrderStatus(
    id: number,
    dto: UpdateOrderStatusDto,
  ): Promise<AdminOrderResponse> {
    const { orderStatus, paymentStatus, deliveryDriverId } = dto;

    const order = await this.prisma.$transaction(async (tx) => {
      const order = await tx.order.update({
        where: { id },
        data: {
          status: orderStatus,
          paymentStatus,
          deliveryDriverId: deliveryDriverId,
          deliveryDriverAssignedDate: deliveryDriverId ? new Date() : undefined,
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  thumbnail: true,
                  category: true,
                },
              },
              variation: {
                include: {
                  options: {
                    include: {
                      option: {
                        include: {
                          attribute: true,
                        },
                      },
                    },
                  },
                  media: true,
                },
              },
              inventory: {
                include: {
                  batch: {
                    include: {
                      warehouse: true,
                    },
                  },
                },
              },
            },
          },
          address: {
            include: {
              country: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              type: true,
              countryId: true,
              profilePicture: true,
              createdAt: true,
              updatedAt: true,
              firebaseToken: true,
              phoneCountry: true,
              tierExpiresAt: true,
              rewardTier: true,
              rewardTierId: true,
              referralCode: true,
              referredById: true,
            },
          },
          orderStatusHistory: true,
          deliveryDriver: {
            include: {
              phoneCountry: true,
            },
          },
          warehouse: true,
        },
      });

      if (orderStatus === 'CANCELLED') {
        await tx.inventoryTransaction.createMany({
          data: order.items.map((item) => ({
            type: 'RETURN',
            quantity: item.quantity,
            inventoryId: item.inventoryId,
            orderId: order.id,
          })),
        });
      }

      if (orderStatus) {
        const latestStatus = await tx.orderStatusHistory.create({
          data: { status: orderStatus, orderId: id },
        });

        order.orderStatusHistory.push(latestStatus);
      }

      return order;
    });

    await this.rewardTiersService.handleQualifyingOrder(order.id);

    const orderUser = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: order.userId,
      },
    });

    if (orderUser.firebaseToken && orderStatus) {
      await this.notificationService.sendToDevice({
        token: orderUser.firebaseToken,
        title: 'Order has been updated',
        body: `Your order #${order.id} is now ${orderStatus.toLowerCase()}`,
      });

      // await this.emailService.sendNewOrderReceivedEmail(orderUser.email, order);
      if (orderStatus === 'CONFIRMED') {
        await this.emailService.sendOrderConfirmationEmail(
          orderUser.email,
          order as OrderResponse,
        );
      } else if (orderStatus === 'READY') {
        await this.emailService.sendOrderAcceptedEmail(
          orderUser.email,
          order as OrderResponse,
        );
      } else if (orderStatus === 'SHIPPED') {
        await this.emailService.sendOrderShippedEmail(
          orderUser.email,
          order as OrderResponse,
        );
      }
    }

    if (deliveryDriverId) {
      const deliveryDriverUser = await this.prisma.user.findUniqueOrThrow({
        where: {
          id: deliveryDriverId,
        },
      });

      if (deliveryDriverUser.firebaseToken) {
        await this.notificationService.sendToDevice({
          token: deliveryDriverUser.firebaseToken,
          title: 'You have a new order to deliver',
          body: `Order #${order.id} is now ready for you.`,
        });
      }
    }

    // Transform the order using the admin order transformer
    return await this.adminOrderTransformer.getAdminOrderResponse(order);
  }

  async updateOrderDelivery(
    id: number,
    dto: UpdateOrderDeliveryDto,
  ): Promise<AdminOrderResponse> {
    const { deliveryDate, deliveryStartTime, deliveryEndTime } = dto;

    const order = await this.prisma.order.update({
      where: { id },
      data: {
        deliveryDate,
        deliveryStartTime,
        deliveryEndTime,
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                thumbnail: true,
              },
            },
            variation: {
              include: {
                options: {
                  include: {
                    option: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
                media: true,
              },
            },
            inventory: {
              include: {
                inventoryTransactions: true,
                batch: {
                  include: {
                    warehouse: true,
                  },
                },
              },
            },
          },
        },
        address: {
          include: {
            country: true,
          },
        },
        user: {
          select: {
            id: true,
            createdAt: true,
            updatedAt: true,
            firebaseToken: true,
            name: true,
            phone: true,
            countryId: true,
            phoneCountry: true,
            profilePicture: true,
            rewardTierId: true,
            referralCode: true,
            referredById: true,
            email: true,
            type: true,
            orders: true,
            tierExpiresAt: true,
          },
        },
        orderStatusHistory: true,
        deliveryDriver: {
          include: {
            phoneCountry: true,
          },
        },
        warehouse: true,
      },
    });

    // Send notification to customer about delivery time update
    if (order.user.firebaseToken) {
      const formattedDate = deliveryDate.toLocaleDateString();
      await this.notificationService.sendToDevice({
        token: order.user.firebaseToken,
        title: 'Delivery time updated',
        body: `Your order #${order.id} delivery has been rescheduled to ${formattedDate} between ${deliveryStartTime} - ${deliveryEndTime}`,
        data: {
          screen: 'order-details',
          orderId: order.id.toString(),
        },
      });
    }

    const totalOrders = order.user.orders.length;
    const totalOrderPrice = order.user.orders.reduce(
      (acc, curr) => acc + +curr.totalAmount,
      0,
    );

    const averageOrderprice = totalOrderPrice / totalOrders;

    // Process the order with inventory quantity
    const processedOrder = {
      ...order,
      user: {
        ...order.user,
        totalOrders: totalOrders,
        totalOrderPrice: totalOrderPrice.toFixed(2),
        averageOrderPrice: averageOrderprice.toFixed(2),
      },
      items: order.items.map((item) => ({
        ...item,
        inventory: {
          ...item.inventory,
          quantity: this.inventoryService.getQuantityOfInventoryItem(
            item.inventory,
          ),
        },
      })),
    };

    // Transform the order using the admin order transformer
    return await this.adminOrderTransformer.getAdminOrderResponse(
      processedOrder,
    );
  }
}
