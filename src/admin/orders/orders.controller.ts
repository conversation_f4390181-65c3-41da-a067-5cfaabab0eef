import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { PermissionGuard } from 'src/auth/permission.guard';
import { CanView, CanManage } from 'src/auth/permission.decorator';
import { PermissionFeature } from '@prisma/client';
import { OrdersService } from './orders.service';
import { UpdateOrderStatusDto, UpdateOrderDeliveryDto } from './orders.schema';

@Controller('orders')
@UseGuards(AuthGuard, PermissionGuard)
@UsePipes(ZodValidationPipe)
export class OrdersController {
  constructor(private ordersService: OrdersService) {}

  @Get()
  @CanView(PermissionFeature.ORDER)
  async getOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.ordersService.getOrders({
      page: page ?? 1,
      pageSize: pageSize ?? 10,
    });
  }

  @Get('/:id')
  @CanView(PermissionFeature.ORDER)
  async getOrderById(@Param('id', ParseIntPipe) id: number) {
    return this.ordersService.getOrderById(id);
  }

  @Patch('/:id')
  @CanManage(PermissionFeature.ORDER)
  async updateOrderStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderStatusDto: UpdateOrderStatusDto,
  ) {
    return this.ordersService.updateOrderStatus(id, updateOrderStatusDto);
  }

  @Patch('/:id/delivery')
  @CanManage(PermissionFeature.ORDER)
  async updateOrderDelivery(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderDeliveryDto: UpdateOrderDeliveryDto,
  ) {
    return this.ordersService.updateOrderDelivery(id, updateOrderDeliveryDto);
  }
}
