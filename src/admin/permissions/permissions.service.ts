import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { AccessLevel, UserType, PermissionFeature } from '@prisma/client';

@Injectable()
export class PermissionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get access level hierarchy - higher levels include lower levels
   * DELETE includes MANAGE and VIEW
   * MANAGE includes VIEW
   */
  private getAccessLevelHierarchy(accessLevel: AccessLevel): AccessLevel[] {
    switch (accessLevel) {
      case AccessLevel.DELETE:
        return [AccessLevel.DELETE, AccessLevel.MANAGE, AccessLevel.VIEW];
      case AccessLevel.MANAGE:
        return [AccessLevel.MANAGE, AccessLevel.VIEW];
      case AccessLevel.VIEW:
        return [AccessLevel.VIEW];
      default:
        return [];
    }
  }

  /**
   * Check if a user has a specific access level for a feature
   * @param userId - The user ID to check
   * @param feature - The feature enum value
   * @param accessLevel - The access level to check (VIEW, MANAGE, DELETE)
   * @returns Promise<boolean> - Whether the user has the required permission
   */
  async hasPermission(
    userId: number,
    feature: PermissionFeature,
    accessLevel: AccessLevel,
  ): Promise<boolean> {
    // Get all access levels that satisfy the required level (hierarchical)
    const allowedAccessLevels = this.getAccessLevelHierarchy(accessLevel);

    // First check if user has custom roles with this permission
    const userRolePermission = await this.prisma.userRole.findFirst({
      where: {
        userId,
        role: {
          active: true,
          permissions: {
            some: {
              feature,
              accessLevel: {
                in: allowedAccessLevels,
              },
            },
          },
        },
      },
    });

    if (userRolePermission) {
      return true;
    }

    // Fallback to UserType-based permissions for backward compatibility
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { type: true },
    });

    if (!user) {
      return false;
    }

    // Define default permissions for system roles
    return this.hasSystemRolePermission(user.type, feature, accessLevel);
  }

  /**
   * Get all permissions for a user
   * @param userId - The user ID
   * @returns Promise with user's permissions grouped by feature
   */
  async getUserPermissions(userId: number) {
    const userRoles = await this.prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                feature: true,
              },
            },
          },
        },
      },
    });

    // Aggregate permissions from all roles
    const permissionMap = new Map<PermissionFeature, Set<AccessLevel>>();

    userRoles.forEach((userRole) => {
      userRole.role.permissions.forEach((permission) => {
        const featureName = permission.feature;
        if (!permissionMap.has(featureName)) {
          permissionMap.set(featureName, new Set());
        }
        permissionMap.get(featureName)!.add(permission.accessLevel);
      });
    });

    // Convert to object format
    const permissions: Record<PermissionFeature, AccessLevel[]> = {} as Record<
      PermissionFeature,
      AccessLevel[]
    >;
    permissionMap.forEach((accessLevels, featureName) => {
      permissions[featureName] = Array.from(accessLevels);
    });

    return permissions;
  }

  /**
   * Check if a user can perform any action on a feature
   * @param userId - The user ID
   * @param feature - The feature enum value
   * @returns Promise<boolean> - Whether user has any permission for the feature
   */
  async hasAnyPermission(
    userId: number,
    feature: PermissionFeature,
  ): Promise<boolean> {
    return (
      (await this.hasPermission(userId, feature, AccessLevel.VIEW)) ||
      (await this.hasPermission(userId, feature, AccessLevel.MANAGE)) ||
      (await this.hasPermission(userId, feature, AccessLevel.DELETE))
    );
  }

  /**
   * Default system role permissions for backward compatibility
   */
  private hasSystemRolePermission(
    userType: UserType,
    feature: PermissionFeature,
    accessLevel: AccessLevel,
  ): boolean {
    // Admin has all permissions
    if (userType === UserType.ADMIN) {
      return true;
    }

    // Get all access levels that satisfy the required level (hierarchical)
    const allowedAccessLevels = this.getAccessLevelHierarchy(accessLevel);

    // Define specific permissions for other user types
    const systemPermissions: Record<
      UserType,
      Record<PermissionFeature, AccessLevel[]>
    > = {
      [UserType.ADMIN]: {} as Record<PermissionFeature, AccessLevel[]>, // Already handled above
      [UserType.CUSTOMER]: {} as Record<PermissionFeature, AccessLevel[]>, // No admin permissions
      [UserType.WAREHOUSE_STAFF]: {
        [PermissionFeature.ORDER]: [AccessLevel.VIEW, AccessLevel.MANAGE],
        [PermissionFeature.INVENTORY]: [AccessLevel.VIEW, AccessLevel.MANAGE],
        [PermissionFeature.PRODUCT]: [AccessLevel.VIEW],
      } as Record<PermissionFeature, AccessLevel[]>,
      [UserType.DELIVERY_PERSON]: {
        [PermissionFeature.ORDER]: [AccessLevel.VIEW, AccessLevel.MANAGE],
      } as Record<PermissionFeature, AccessLevel[]>,
    };

    const userPermissions = systemPermissions[userType];
    const featurePermissions = userPermissions[feature];

    if (!featurePermissions) {
      return false;
    }

    // Check if user has any of the allowed access levels for this feature
    return featurePermissions.some((level) =>
      allowedAccessLevels.includes(level),
    );
  }

  /**
   * Bulk check permissions for multiple features
   * @param userId - The user ID
   * @param checks - Array of {feature, accessLevel} to check
   * @returns Promise<Record<string, boolean>> - Results keyed by "feature:accessLevel"
   */
  async checkMultiplePermissions(
    userId: number,
    checks: Array<{ feature: PermissionFeature; accessLevel: AccessLevel }>,
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const check of checks) {
      const key = `${check.feature}:${check.accessLevel}`;
      results[key] = await this.hasPermission(
        userId,
        check.feature,
        check.accessLevel,
      );
    }

    return results;
  }
}
