import { SetMetadata } from '@nestjs/common';
import { AccessLevel, PermissionFeature } from '@prisma/client';

export interface PermissionRequirement {
  feature: PermissionFeature;
  accessLevel: AccessLevel | 'ANY';
}

export const PERMISSION_KEY = 'permissions';

/**
 * Decorator to require specific permissions for a route
 * @param feature - The feature enum value (e.g., PermissionFeature.ORDER)
 * @param accessLevel - The required access level (VIEW, MANAGE, DELETE)
 */
export const RequirePermission = (
  feature: PermissionFeature,
  accessLevel: AccessLevel | 'ANY',
) =>
  SetMetadata(PERMISSION_KEY, {
    feature,
    accessLevel,
  } as PermissionRequirement);

/**
 * Convenience decorators for common permission patterns
 */
export const CanView = (feature: PermissionFeature) =>
  RequirePermission(feature, AccessLevel.VIEW);
export const CanManage = (feature: PermissionFeature) =>
  RequirePermission(feature, AccessLevel.MANAGE);
export const CanDelete = (feature: PermissionFeature) =>
  RequirePermission(feature, AccessLevel.DELETE);

/**
 * Decorator to require any permission for a feature (VIEW, MANAGE, or DELETE)
 * @param feature - The feature enum value
 */
export const RequireAnyPermission = (feature: PermissionFeature) =>
  RequirePermission(feature, 'ANY');
