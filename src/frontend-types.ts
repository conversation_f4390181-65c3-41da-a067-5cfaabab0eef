export interface User {
  name: string;
  createdAt: Date;
  updatedAt: Date;
  id: number;
  email: string;
  type: UserType;
  phone: string;
  phoneCountry: Country;
  countryId: number;
  profilePicture: string | null;
  addresses: Address[];
  referralCode: string;
  referrals: { id: number }[];
  rewardTier: RewardTier | null;
  rewardTierId: number | null;
  rewardPoints: number;
  performance?: {
    totalOrderCount: number;
    avgPickupMins: number;
    avgDeliveryMins: number;
    lateDeliveryCount: number;
    avgLateMins: number;
  };
  lastLocation?: {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    lat: string;
    long: string;
    driverId: number;
  } | null;
  distanceFromWarehouse?: {
    distance: number;
    unit: string;
    lastUpdated: Date;
  } | null;
  totalOrders?: number;
  totalOrderPrice?: string;
  averageOrderPrice?: string;
  deliveryDriverProfile?: {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    userId: number;
    isActive: boolean;
  };
}

export const USER_TYPES = [
  "ADMIN",
  "CUSTOMER",
  "WAREHOUSE_STAFF",
  "DELIVERY_PERSON",
] as const;
export type UserType = (typeof USER_TYPES)[number]; // "hearts" | "diamonds" | "spades" | "clubs"

// export type UserType =
//   | "ADMIN"
//   | "CUSTOMER"
//   | "WAREHOUSE_STAFF"
//   | "DELIVERY_PERSON";

export interface PaginationData {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends PaginationData {
  data: T[];
}

export interface Media {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  url: string;
  mediaType: "IMAGE" | "VIDEO";
  variationId: number | null;
  productId: number;
}

export const WeightUnit = [
  "GRAM",
  "MILLILITER",
  "PIECE",
  "KILOGRAM",
  "LITER",
] as const;
export type WeightUnit = (typeof WeightUnit)[number];

export interface Product {
  createdAt: Date;
  updatedAt: Date;
  active: boolean;
  id: number;
  categoryId: number;
  category: Category;
  name: string;
  barcode: string;
  description: string;
  slug: string;
  gstPercentage: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  highlights: Record<string, any> | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  information: Record<string, any> | null;
  weight: number;
  weightUnit: WeightUnit;
  length: number;
  width: number;
  height: number;
  media: Media[];
  thumbnail: Media | null;
  thumbnailId: number | null;
  discountValue: number | null;
  discountType: AmountType | null;
  relatedProducts?: Product[];
  hasVariations: boolean;
  productPolicies?: ProductPolicy[];
  maxCartQuantity: number | null;
  price: number;
  quantity: number;
  variations?: {
    id: number;
    name: string;
    price: number;
    stock: number;
  }[];
}

export const OrderStatus = [
  "PENDING",
  "CONFIRMED",
  "PACKING",
  "READY",
  "SHIPPED",
  "DELIVERED",
  "CANCELLED",
] as const;

export type OrderStatus = (typeof OrderStatus)[number];

export type PaymentStatus = "PENDING" | "PAID" | "FAILED" | "REFUNDED";

export interface Order {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  userId: number;
  status: OrderStatus;
  addressId: number;
  deliveryDate: Date;
  deliveryStartTime: string;
  deliveryEndTime: string;
  note?: string;
  paymentStatus: PaymentStatus;
  cashToPay: string;
  subtotal: string;
  handlingCharge: string;
  deliveryFee: string;
  discountAmount: string;
  couponDiscount: string;
  rewardDiscount: string;
  totalAmount: string;
  items: OrderItem[];
  address: Address;
  user: User & {
    totalOrders: number;
    totalOrderPrice: string;
    averageOrderPrice: string;
  };
  orderStatusHistory: OrderStatusHistory[];
  coupon?: Coupon;
  deliveryDriverId?: number;
  deliveryDriver?: User;
  warehouse?: Warehouse;
}

export const AmountType = ["FLAT", "PERCENTAGE"] as const;
export type AmountType = (typeof AmountType)[number];

export interface Coupon {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  code: string;
  discountValue: number;
  discountType: AmountType;
  minOrderValue?: number;
  maxDiscount?: number;
  usageLimit?: number;
  perUserLimit?: number;
  expiresAt?: Date;
  isActive: boolean;
}

export interface Address {
  id: number;
  streetName: string;
  apartment?: string;
  block?: string;
  city: string;
  state: string;
  countryId: number;
  zipCode: string;
  lat: string;
  long: string;
}

export interface OrderItem {
  id: number;
  orderId: number;
  productId: number;
  quantity: number;
  originalPrice: string;
  price: string;
  gstAmount: string;
  inventoryId: number;
  variationId?: number;
  product: Product;
  inventory: Inventory;
  variation: {
    id: number;
    productId: number;
    barcode: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
    media: Media[];
    options: {
      option: {
        id: number;
        name: string;
        colorCode: string | null;
        imageUrl: string | null;
        attribute: {
          id: number;
          name: string;
        };
      };
    }[];
  };
}

export interface OrderStatusHistory {
  createdAt: Date;
  id: number;
  orderId: number;
  status: OrderStatus;
}

export interface InventoryBatch {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  name: string;
  warehouse: Warehouse;
}

export interface Inventory {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  batchId: number;
  productId: number;
  product: Product;
  buyingPrice: string;
  sellingPrice: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierId: number | null;
  quantity: number;
  inventoryTransactions: InventoryTransaction[];
  batch: InventoryBatch;
}

export interface InventoryTransaction {
  createdAt: Date;
  id: number;
  inventoryId: number;
  quantity: number;
  type: string;
  orderId: number | null;
  remark: string | null;
}

export const CategoryType = ["COLLECTION", "CATEGORY", "SEGMENT"] as const;
export type CategoryType = (typeof CategoryType)[number];

export interface Category {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  name: string;
  parentId: number | null;
  parent?: Category;
  type: CategoryType;
  slug: string;
  isActive: boolean;
  bannerUrl?: string | null;
  iconUrl?: string | null;
}

export type Zone = {
  id: number;
  name: string;
  coordinates: {
    lat: string;
    long: string;
  }[];
};

export type WarehouseType = "GENERAL" | "SUPER";

export interface Warehouse {
  id: number;
  name: string;
  address: string;
  lat: string;
  long: string;
  type: WarehouseType;
  active: boolean;
}

export type Supplier = {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  countryId: number;
  country: Country;
};

export type Country = {
  id: number;
  name: string;
  code: string;
  dialCode: string;
  flagUrl: string;
};

export type RewardTierType =
  | "NONE"
  | "BRONZE"
  | "SILVER"
  | "GOLD"
  | "PLATINUM"
  | "DIAMOND";

export interface RewardTier {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  name: string;
  type: RewardTierType;
  requiredRollingSpend: number;
  earnPercentage: number;
  redeemPercentage: number;
  maxDiscountValue: number;
  minOrderAmountForBonus: number | null;
}

export type RewardPointConfig = {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  minOrderAmountForBonus: string;
  fixedBonusPoints: number;
  referralBonusPoints: number;
  active: boolean;
};

export type ProductTranslation = {
  id: number;
  languageId: number;
  language: Language;
  name: string;
  description: string;
  productId: number;
};

export type CategoryTranslation = {
  categoryId: number;
  language: Language;
  languageId: number;
  name: string;
};

export type Language = {
  id: number;
  name: string;
  code: string;
  flagUrl: string;
};

export type Weekday =
  | "SUNDAY"
  | "MONDAY"
  | "TUESDAY"
  | "WEDNESDAY"
  | "THURSDAY"
  | "FRIDAY"
  | "SATURDAY";

export type DeliverySlotType = "REGULAR" | "HOLIDAY";

export type DeliverySlot = {
  id: number;
  startTime: string | null;
  endTime: string | null;
  date: Date | null;
  weekday: Weekday | null;
  type: DeliverySlotType;
  reason?: string | null;
};

export type ErrorResponse = {
  statusCode: number;
  message: string;
  errors: {
    code: string;
    message: string;
    path: string[];
  }[];
};

export type HomeSectionType = "CATEGORY" | "PRODUCT";

export interface HomeSection {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  title: string;
  onlyDiscount: boolean;
  type: HomeSectionType;
  warehouseId: number | null;
  warehouse?: Warehouse | null;
  displayOrder: number;
  homeSectionCategory?: HomeSectionCategory[];
  translations?: HomeSectionTranslation[];
}

export interface HomeSectionCategory {
  id: string;
  homeSectionId: string;
  categoryId: number;
  category?: Category;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface HomeSectionTranslation {
  id: number;
  homeSectionId: string;
  languageId: number;
  language: Language;
  title: string;
}

export interface ProductPolicyType {
  id: number;
  name: string;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductPolicy {
  id: number;
  productId: number;
  description: string | null;
  details: Record<string, string>;
  productPolicyTypeId: number;
  productPolicyType: ProductPolicyType;
  createdAt: Date;
  updatedAt: Date;
}

// Permission system types
export const ACCESS_LEVELS = ["VIEW", "MANAGE", "DELETE"] as const;
export type AccessLevel = (typeof ACCESS_LEVELS)[number];

export const PERMISSION_FEATURES = [
  "ORDER",
  "PRODUCT",
  "USER",
  "INVENTORY",
  "ANALYTICS",
  "WAREHOUSE",
  "COUPON",
  "NOTIFICATION",
] as const;
export type PermissionFeature = (typeof PERMISSION_FEATURES)[number];

export interface Role {
  id: number;
  name: string;
  description?: string;
  isSystemRole: boolean;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  permissions?: RolePermission[];
  userRoles?: UserRole[];
  _count?: {
    userRoles: number;
  };
}

export interface RolePermission {
  roleId: number;
  feature: PermissionFeature;
  accessLevel: AccessLevel;
  role?: Role;
}

export interface UserRole {
  userId: number;
  roleId: number;
  assignedAt: Date;
  user?: User;
  role?: Role;
}
