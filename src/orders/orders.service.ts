import { Injectable } from '@nestjs/common';
import { Prisma, WarehouseType } from '@prisma/client';
import { InventoryService } from 'src/admin/inventories/inventories.service';
import { NotificationService } from 'src/admin/notification/notification.service';
import { CartService } from 'src/cart/cart.service';
import { Context } from 'src/context';
import { Exception, NoWarehouseException } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { OrderTransformer } from 'src/transformers/order.transformer';
import { OrderQueryResult, OrderResponse, UserSafe } from 'src/types';
import { CreateOrderDto } from './orders.schema';
import { isAllowedToOverrideUser } from 'src/util/utils';
import { WarehouseService } from 'src/admin/warehouse/warehouse.service';
import { EmailService } from 'src/email/email.service';

@Injectable()
export class OrdersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly cartService: CartService,
    private readonly inventoryService: InventoryService,
    private readonly notificationService: NotificationService,
    private readonly orderTransformer: OrderTransformer,
    private readonly warehouseService: WarehouseService,
    private readonly emailService: EmailService,
  ) {}

  async getOrders(params: {
    page?: number;
    pageSize?: number;
    lang: string;
    overrideUser?: UserSafe;
  }) {
    const { page = 1, pageSize = 10, lang, overrideUser } = params;

    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const [orderQueryResults, total] = await this.prisma.$transaction(
      async (tx) => {
        const orders = await tx.order.findMany({
          skip: (page - 1) * pageSize,
          take: pageSize,
          where: {
            userId: user.id,
          },
          include: {
            items: {
              include: {
                product: {
                  include: {
                    media: true,
                    category: {
                      include: {
                        categoryTranslation: {
                          where: { languageId: language.id },
                          take: 1,
                        },
                      },
                    },
                    productTranslation: {
                      where: { languageId: language.id },
                      take: 1,
                    },
                    thumbnail: true,
                  },
                },
                variation: {
                  include: {
                    options: {
                      include: {
                        option: {
                          include: {
                            attribute: true,
                          },
                        },
                      },
                    },
                    media: true,
                  },
                },
              },
            },
            address: {
              include: {
                country: true,
              },
            },
            orderStatusHistory: true,
            deliveryDriver: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
                updatedAt: true,
                type: true,
                phone: true,
                profilePicture: true,
                countryId: true,
                phoneCountry: true,
                rewardTierId: true,
                rewardTier: true,
                referralCode: true,
                referredById: true,
                firebaseToken: true,
                tierExpiresAt: true,
              },
            },
            warehouse: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        const total = await tx.order.count({
          where: {
            userId: user.id,
          },
        });

        return [orders, total];
      },
    );

    const orders = await Promise.all(
      orderQueryResults.map(async (order) => {
        const orderResponse =
          await this.orderTransformer.getOrderResponse(order);
        return {
          ...orderResponse,
          user: user,
        } satisfies OrderResponse;
      }),
    );

    return {
      data: orders,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getOrder(id: number, lang: string, overrideUser?: UserSafe) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const orderQueryResult: OrderQueryResult =
      await this.prisma.order.findUniqueOrThrow({
        where: {
          id,
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  media: true,
                  category: {
                    include: {
                      categoryTranslation: {
                        where: { languageId: language.id },
                        take: 1,
                      },
                    },
                  },
                  productTranslation: {
                    where: { languageId: language.id },
                    take: 1,
                  },
                  thumbnail: true,
                },
              },
              variation: {
                include: {
                  options: {
                    include: {
                      option: {
                        include: {
                          attribute: true,
                        },
                      },
                    },
                  },
                  media: true,
                },
              },
            },
          },
          address: {
            include: {
              country: true,
            },
          },
          orderStatusHistory: true,
          deliveryDriver: {
            select: {
              id: true,
              name: true,
              email: true,
              createdAt: true,
              updatedAt: true,
              type: true,
              phone: true,
              profilePicture: true,
              countryId: true,
              phoneCountry: true,
              rewardTierId: true,
              rewardTier: true,
              referralCode: true,
              referredById: true,
              firebaseToken: true,
              tierExpiresAt: true,
            },
          },
          warehouse: true,
        },
      });

    const order: OrderResponse =
      await this.orderTransformer.getOrderResponse(orderQueryResult);

    return { ...order, user: user } satisfies OrderResponse;
  }

  async createOrder(createOrderDto: CreateOrderDto, overrideUser?: UserSafe) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;
    const {
      deliveryDate,
      deliveryStartTime,
      deliveryEndTime,
      shippingAddressId,
      note,
      lat,
      long,
      warehouseType = WarehouseType.GENERAL,
      couponCode,
      useRewardPoints = false,
      overrideWarehouseId,
    } = createOrderDto;

    const cart = await this.cartService.getCart({
      lat,
      long,
      warehouseType,
      couponCode,
      useRewardPoints,
      lang: 'en',
      overrideUser: overrideUser,
      overrideWarehouseId,
    });

    if (cart.items.length === 0) {
      throw new Exception('Cart is empty. Cannot create order.');
    }

    if (!cart.canOrder) {
      throw new Exception('Cannot create order with the current cart items');
    }

    const warehouse = overrideWarehouseId
      ? await this.warehouseService.getWarehouseById(overrideWarehouseId)
      : await this.warehouseService.getNearestWarehouse(
          { lat, long },
          warehouseType,
        );

    if (!warehouse) {
      throw new NoWarehouseException();
    }

    const orderItems: Prisma.OrderItemCreateManyOrderInput[] = [];
    for (const cartItem of cart.items) {
      // Get inventory based on whether a variation is specified
      const inventory =
        await this.inventoryService.getProductInventoryByWarehouse(
          cartItem.productId,
          warehouse.id,
          cartItem.variationId ?? undefined,
        );

      if (inventory.quantity < cartItem.quantity) {
        throw new Exception(
          `Insufficient stock for "${cartItem.product.name}". Only ${inventory.quantity} available.`,
        );
      }

      orderItems.push({
        productId: cartItem.productId,
        variationId: cartItem.variationId,
        quantity: cartItem.quantity,
        price: cartItem.itemTotal,
        originalPrice: cartItem.originalTotal,
        gstAmount: cartItem.gstAmount,
        inventoryId: inventory.id,
      } satisfies Prisma.OrderItemCreateManyOrderInput);
    }

    let couponId: number | null = null;
    if (cart.couponValid && couponCode) {
      const coupon = await this.prisma.coupon.findUnique({
        where: {
          code: couponCode,
        },
      });
      couponId = coupon?.id ?? null;
    }

    const order = await this.prisma.$transaction(async (tx) => {
      const createdOrder = await tx.order.create({
        data: {
          userId: user.id,
          items: {
            createMany: {
              data: orderItems,
            },
          },
          deliveryDate: new Date(deliveryDate),
          deliveryStartTime: deliveryStartTime,
          deliveryEndTime: deliveryEndTime,
          paymentStatus: 'PENDING',
          status: 'PENDING',
          subtotal: Number(cart.subtotal),
          handlingCharge: Number(cart.handlingCharge),
          deliveryFee: Number(cart.deliveryFee),
          totalAmount: Number(cart.total),
          couponId: cart.couponValid ? couponId : null,
          couponDiscount: parseFloat(cart.couponDiscount),
          useRewardPoints,
          rewardPointsUsed: cart.rewardPointsUsed,
          rewardDiscount: parseFloat(cart.rewardDiscount),
          addressId: shippingAddressId,
          note,
          orderStatusHistory: {
            create: {
              status: 'PENDING',
            },
          },
          warehouseId: warehouse.id,
        },
        include: {
          warehouse: true,
          items: {
            include: {
              product: {
                include: {
                  thumbnail: true,
                  media: true,
                  category: true,
                },
              },
              variation: {
                include: {
                  options: {
                    include: {
                      option: {
                        include: {
                          attribute: true,
                        },
                      },
                    },
                  },
                  media: true,
                },
              },
            },
          },
          address: {
            include: {
              country: true,
            },
          },
          orderStatusHistory: true,
        },
      });

      await tx.inventoryTransaction.createMany({
        data: orderItems.map((item) => ({
          type: 'SALE',
          quantity: item.quantity,
          inventoryId: item.inventoryId,
          orderId: createdOrder.id,
          byUserId: user.id,
        })),
      });

      if (useRewardPoints && cart.rewardPointsUsed > 0) {
        await tx.rewardPointTransaction.create({
          data: {
            userId: user.id,
            type: 'DEBIT',
            amount: cart.rewardPointsUsed,
            note: `Used for order #${createdOrder.id}`,
            orderId: createdOrder.id,
          },
        });
      }

      await this.cartService.clearCart(overrideUser);

      // Find warehouse staff assigned to the warehouse
      const warehouseStaff = await tx.warehouseStaff.findMany({
        where: {
          warehouseId: warehouse.id,
        },
        include: {
          user: true,
        },
      });

      // Send notifications to warehouse staff
      for (const staff of warehouseStaff) {
        if (staff.user.firebaseToken) {
          await this.notificationService.sendToDevice({
            token: staff.user.firebaseToken,
            title: 'New Order Received',
            body: `Order #${createdOrder.id} has been assigned to your warehouse`,
            data: {
              screen: 'order-details',
              orderId: createdOrder.id.toString(),
            },
          });
        }
      }

      return { ...createdOrder, user } satisfies OrderResponse;
    });

    if (user.firebaseToken) {
      await this.notificationService.sendToDevice({
        token: user.firebaseToken,
        title: 'Order has been placed',
        body: `Your order ID is #${order.id}`,
      });

      await this.emailService.sendNewOrderReceivedEmail(user.email, order);
    }

    return order;
  }

  async cancelOrder(id: number, overrideUser?: UserSafe) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const order = await this.prisma.order.findUniqueOrThrow({
      where: { id },
      include: {
        items: true,
      },
    });

    if (order.status !== 'PENDING') {
      throw new Exception('Order cannot be cancelled');
    }

    const updatedOrder = await this.prisma.$transaction(async (tx) => {
      const updatedOrder = await tx.order.update({
        where: { id },
        data: { status: 'CANCELLED' },
        include: {
          items: true,
        },
      });

      await tx.inventoryTransaction.createMany({
        data: updatedOrder.items.map((item) => ({
          type: 'RETURN',
          quantity: item.quantity,
          inventoryId: item.inventoryId,
          orderId: updatedOrder.id,
          byUserId: user.id,
        })),
      });

      return updatedOrder;
    });

    return updatedOrder;
  }
}
