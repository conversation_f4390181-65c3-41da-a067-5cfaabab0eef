import 'package:vegmove_delivery/domain/model/inventory.dart';
import 'package:vegmove_delivery/domain/model/product.dart';
import 'package:vegmove_delivery/domain/model/product_variation.dart';

class Item {
  final int id;
  final int orderId;
  final int productId;
  final int quantity;
  final String price;
  final String gstAmount;
  final int inventoryId;
  final Product product;
  final Inventory inventory;
  final ProductVariation? variation;

  Item({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.gstAmount,
    required this.inventoryId,
    required this.product,
    required this.inventory,
    this.variation,
  });

  Item copyWith({
    int? id,
    int? orderId,
    int? productId,
    int? quantity,
    String? price,
    String? gstAmount,
    int? inventoryId,
    Product? product,
    Inventory? inventory,
    ProductVariation? variation,
  }) =>
      Item(
        id: id ?? this.id,
        orderId: orderId ?? this.orderId,
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
        price: price ?? this.price,
        gstAmount: gstAmount ?? this.gstAmount,
        inventoryId: inventoryId ?? this.inventoryId,
        product: product ?? this.product,
        inventory: inventory ?? this.inventory,
        variation: variation ?? this.variation,
      );

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json['id'],
        orderId: json['orderId'],
        productId: json['productId'],
        quantity: json['quantity'],
        price: json['price'],
        gstAmount: json['gstAmount'],
        inventoryId: json['inventoryId'],
        product: Product.fromJson(json['product']),
        inventory: Inventory.fromJson(json['inventory']),
        variation: json['variation'] != null
            ? ProductVariation.fromJson(json['variation'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'orderId': orderId,
        'productId': productId,
        'quantity': quantity,
        'price': price,
        'gstAmount': gstAmount,
        'inventoryId': inventoryId,
        'product': product.toJson(),
        'inventory': inventory.toJson(),
        'variation': variation?.toJson(),
      };
}
