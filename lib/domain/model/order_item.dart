// To parse this JSON data, do
//
//     final orderItem = orderItemFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/inventory.dart';
import 'package:vegmove_warehouse/domain/model/product.dart';
import 'package:vegmove_warehouse/domain/model/product_variation.dart';

OrderItem orderItemFromJson(String str) => OrderItem.fromJson(json.decode(str));

String orderItemToJson(OrderItem data) => json.encode(data.toJson());

class OrderItem {
  final int id;
  final int orderId;
  final int productId;
  final int quantity;
  final String price;
  final String originalPrice;
  final String gstAmount;
  final int inventoryId;
  final Product product;
  final ProductVariation? variation;
  final int? variationId;
  final Inventory? inventory;

  OrderItem({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.originalPrice,
    required this.gstAmount,
    required this.inventoryId,
    required this.product,
    this.variation,
    this.variationId,
    this.inventory,
  });

  OrderItem copyWith({
    int? id,
    int? orderId,
    int? productId,
    int? quantity,
    String? price,
    String? originalPrice,
    String? gstAmount,
    int? inventoryId,
    Product? product,
    ProductVariation? variation,
    int? variationId,
    Inventory? inventory,
  }) =>
      OrderItem(
        id: id ?? this.id,
        orderId: orderId ?? this.orderId,
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
        price: price ?? this.price,
        originalPrice: originalPrice ?? this.originalPrice,
        gstAmount: gstAmount ?? this.gstAmount,
        inventoryId: inventoryId ?? this.inventoryId,
        product: product ?? this.product,
        variation: variation ?? this.variation,
        variationId: variationId ?? this.variationId,
        inventory: inventory ?? this.inventory,
      );

  factory OrderItem.fromJson(Map<String, dynamic> json) => OrderItem(
        id: json['id'],
        orderId: json['orderId'],
        productId: json['productId'],
        quantity: json['quantity'],
        price: json['price'],
        originalPrice: json['originalPrice'] ?? '0',
        gstAmount: json['gstAmount'],
        inventoryId: json['inventoryId'],
        product: Product.fromJson(json['product']),
        variation: json['variation'] != null
            ? ProductVariation.fromJson(json['variation'])
            : null,
        variationId: json['variationId'],
        inventory: json['inventory'] != null
            ? Inventory.fromJson(json['inventory'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'orderId': orderId,
        'productId': productId,
        'quantity': quantity,
        'price': price,
        'originalPrice': originalPrice,
        'gstAmount': gstAmount,
        'inventoryId': inventoryId,
        'product': product.toJson(),
        'variation': variation?.toJson(),
        'variationId': variationId,
        'inventory': inventory?.toJson(),
      };
}
