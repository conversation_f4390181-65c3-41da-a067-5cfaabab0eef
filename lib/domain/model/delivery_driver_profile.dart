import 'dart:convert';
import 'package:vegmove_delivery/domain/model/user.dart';

DeliveryDriverProfile deliveryDriverProfileFromJson(String str) =>
    DeliveryDriverProfile.fromJson(json.decode(str));

String deliveryDriverProfileToJson(DeliveryDriverProfile data) =>
    json.encode(data.toJson());

class DeliveryDriverProfile {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int userId;
  final bool isActive;
  final User user;

  DeliveryDriverProfile({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.userId,
    required this.isActive,
    required this.user,
  });

  DeliveryDriverProfile copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? userId,
    bool? isActive,
    User? user,
  }) =>
      DeliveryDriverProfile(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        userId: userId ?? this.userId,
        isActive: isActive ?? this.isActive,
        user: user ?? this.user,
      );

  factory DeliveryDriverProfile.fromJson(Map<String, dynamic> json) =>
      DeliveryDriverProfile(
        id: json['id'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        userId: json['userId'],
        isActive: json['isActive'],
        user: User.fromJson(json['user']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'userId': userId,
        'isActive': isActive,
        'user': user.toJson(),
      };
}
