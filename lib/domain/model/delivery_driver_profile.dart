import 'dart:convert';

DeliveryDriverProfile deliveryDriverProfileFromJson(String str) =>
    DeliveryDriverProfile.fromJson(json.decode(str));

String deliveryDriverProfileToJson(DeliveryDriverProfile data) =>
    json.encode(data.toJson());

class DeliveryDriverProfile {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int userId;
  final bool isActive;

  DeliveryDriverProfile({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.userId,
    required this.isActive,
  });

  DeliveryDriverProfile copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? userId,
    bool? isActive,
  }) =>
      DeliveryDriverProfile(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        userId: userId ?? this.userId,
        isActive: isActive ?? this.isActive,
      );

  factory DeliveryDriverProfile.fromJson(Map<String, dynamic> json) =>
      DeliveryDriverProfile(
        id: json["id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        userId: json["userId"],
        isActive: json["isActive"] ?? true,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "userId": userId,
        "isActive": isActive,
      };
}
