class Address {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int userId;
  final String type;
  final String apartment;
  final String block;
  final String streetName;
  final String city;
  final String state;
  final int countryId;
  final String zipCode;
  final String lat;
  final String long;

  Address({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.userId,
    required this.type,
    required this.apartment,
    required this.block,
    required this.streetName,
    required this.city,
    required this.state,
    required this.countryId,
    required this.zipCode,
    required this.lat,
    required this.long,
  });

  Address copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? userId,
    String? type,
    String? apartment,
    String? block,
    String? streetName,
    String? city,
    String? state,
    int? countryId,
    String? zipCode,
    String? lat,
    String? long,
  }) =>
      Address(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        userId: userId ?? this.userId,
        type: type ?? this.type,
        apartment: apartment ?? this.apartment,
        block: block ?? this.block,
        streetName: streetName ?? this.streetName,
        city: city ?? this.city,
        state: state ?? this.state,
        countryId: countryId ?? this.countryId,
        zipCode: zipCode ?? this.zipCode,
        lat: lat ?? this.lat,
        long: long ?? this.long,
      );

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        userId: json['userId'],
        type: json['type'],
        apartment: json['apartment'],
        block: json['block'],
        streetName: json['streetName'],
        city: json['city'],
        state: json['state'],
        countryId: json['countryId'],
        zipCode: json['zipCode'],
        lat: json['lat'],
        long: json['long'],
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'userId': userId,
        'type': type,
        'apartment': apartment,
        'block': block,
        'streetName': streetName,
        'city': city,
        'state': state,
        'countryId': countryId,
        'zipCode': zipCode,
        'lat': lat,
        'long': long,
      };
}
