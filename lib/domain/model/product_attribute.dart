class ProductAttribute {
  final int id;
  final String name;

  ProductAttribute({
    required this.id,
    required this.name,
  });

  ProductAttribute copyWith({
    int? id,
    String? name,
  }) =>
      ProductAttribute(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory ProductAttribute.fromJson(Map<String, dynamic> json) => ProductAttribute(
        id: json['id'],
        name: json['name'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
      };
}
