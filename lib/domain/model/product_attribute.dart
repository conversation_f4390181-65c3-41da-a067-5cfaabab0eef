// To parse this JSON data, do
//
//     final productAttribute = productAttributeFromJson(jsonString);

import 'dart:convert';

ProductAttribute productAttributeFromJson(String str) => ProductAttribute.fromJson(json.decode(str));

String productAttributeToJson(ProductAttribute data) => json.encode(data.toJson());

class ProductAttribute {
  final int id;
  final String name;
  final int productId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ProductAttributeOption> options;

  ProductAttribute({
    required this.id,
    required this.name,
    required this.productId,
    required this.createdAt,
    required this.updatedAt,
    required this.options,
  });

  ProductAttribute copyWith({
    int? id,
    String? name,
    int? productId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ProductAttributeOption>? options,
  }) =>
      ProductAttribute(
        id: id ?? this.id,
        name: name ?? this.name,
        productId: productId ?? this.productId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        options: options ?? this.options,
      );

  factory ProductAttribute.fromJson(Map<String, dynamic> json) => ProductAttribute(
        id: json["id"],
        name: json["name"],
        productId: json["productId"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        options: json["options"] != null
            ? List<ProductAttributeOption>.from(
                json["options"].map((x) => ProductAttributeOption.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "productId": productId,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "options": List<dynamic>.from(options.map((x) => x.toJson())),
      };
}

class ProductAttributeOption {
  final int id;
  final int attributeId;
  final String name;
  final String? colorCode;
  final String? imageUrl;
  final String? iconUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ProductAttribute? attribute;

  ProductAttributeOption({
    required this.id,
    required this.attributeId,
    required this.name,
    this.colorCode,
    this.imageUrl,
    this.iconUrl,
    required this.createdAt,
    required this.updatedAt,
    this.attribute,
  });

  ProductAttributeOption copyWith({
    int? id,
    int? attributeId,
    String? name,
    String? colorCode,
    String? imageUrl,
    String? iconUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    ProductAttribute? attribute,
  }) =>
      ProductAttributeOption(
        id: id ?? this.id,
        attributeId: attributeId ?? this.attributeId,
        name: name ?? this.name,
        colorCode: colorCode ?? this.colorCode,
        imageUrl: imageUrl ?? this.imageUrl,
        iconUrl: iconUrl ?? this.iconUrl,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        attribute: attribute ?? this.attribute,
      );

  factory ProductAttributeOption.fromJson(Map<String, dynamic> json) => ProductAttributeOption(
        id: json["id"],
        attributeId: json["attributeId"],
        name: json["name"],
        colorCode: json["colorCode"],
        imageUrl: json["imageUrl"],
        iconUrl: json["iconUrl"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        attribute: json["attribute"] != null ? ProductAttribute.fromJson(json["attribute"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "attributeId": attributeId,
        "name": name,
        "colorCode": colorCode,
        "imageUrl": imageUrl,
        "iconUrl": iconUrl,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "attribute": attribute?.toJson(),
      };
}
