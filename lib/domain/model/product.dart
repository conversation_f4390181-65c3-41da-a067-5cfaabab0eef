import 'package:vegmove_delivery/domain/model/media.dart';

class Product {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String barcode;
  final String name;
  final String description;
  final dynamic metadata;
  final int categoryId;
  final dynamic subCategoryId;
  final int weight;
  final String weightUnit;
  final int length;
  final int width;
  final int height;
  final int gstPercentage;
  final dynamic supplierId;
  final dynamic slug;
  final dynamic discountValue;
  final dynamic discountType;
  final Media? thumbnail;

  Product({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.barcode,
    required this.name,
    required this.description,
    required this.metadata,
    required this.categoryId,
    required this.subCategoryId,
    required this.weight,
    required this.weightUnit,
    required this.length,
    required this.width,
    required this.height,
    required this.gstPercentage,
    required this.supplierId,
    required this.slug,
    required this.discountValue,
    required this.discountType,
    this.thumbnail,
  });

  Product copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? barcode,
    String? name,
    String? description,
    dynamic metadata,
    int? categoryId,
    dynamic subCategoryId,
    int? weight,
    String? weightUnit,
    int? length,
    int? width,
    int? height,
    int? gstPercentage,
    dynamic supplierId,
    dynamic slug,
    dynamic discountValue,
    dynamic discountType,
    Media? thumbnail,
  }) =>
      Product(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        barcode: barcode ?? this.barcode,
        name: name ?? this.name,
        description: description ?? this.description,
        metadata: metadata ?? this.metadata,
        categoryId: categoryId ?? this.categoryId,
        subCategoryId: subCategoryId ?? this.subCategoryId,
        weight: weight ?? this.weight,
        weightUnit: weightUnit ?? this.weightUnit,
        length: length ?? this.length,
        width: width ?? this.width,
        height: height ?? this.height,
        gstPercentage: gstPercentage ?? this.gstPercentage,
        supplierId: supplierId ?? this.supplierId,
        slug: slug ?? this.slug,
        discountValue: discountValue ?? this.discountValue,
        discountType: discountType ?? this.discountType,
        thumbnail: thumbnail ?? this.thumbnail,
      );

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        barcode: json['barcode'],
        name: json['name'],
        description: json['description'],
        metadata: json['metadata'],
        categoryId: json['categoryId'],
        subCategoryId: json['subCategoryId'],
        weight: json['weight'],
        weightUnit: json['weightUnit'],
        length: json['length'],
        width: json['width'],
        height: json['height'],
        gstPercentage: json['gstPercentage'],
        supplierId: json['supplierId'],
        slug: json['slug'],
        discountValue: json['discountValue'],
        discountType: json['discountType'],
        thumbnail: json['thumbnail'] != null
            ? Media.fromJson(json['thumbnail'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'barcode': barcode,
        'name': name,
        'description': description,
        'metadata': metadata,
        'categoryId': categoryId,
        'subCategoryId': subCategoryId,
        'weight': weight,
        'weightUnit': weightUnit,
        'length': length,
        'width': width,
        'height': height,
        'gstPercentage': gstPercentage,
        'supplierId': supplierId,
        'slug': slug,
        'discountValue': discountValue,
        'discountType': discountType,
        'thumbnail': thumbnail?.toJson(),
      };
}
