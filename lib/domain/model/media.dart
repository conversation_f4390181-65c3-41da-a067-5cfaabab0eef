class Media {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String url;
  final String mediaType;

  Media({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.url,
    required this.mediaType,
  });

  Media copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? url,
    String? mediaType,
  }) =>
      Media(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        url: url ?? this.url,
        mediaType: mediaType ?? this.mediaType,
      );

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        url: json['url'],
        mediaType: json['mediaType'],
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'url': url,
        'mediaType': mediaType,
      };
}
