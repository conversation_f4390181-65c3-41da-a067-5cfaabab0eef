import 'package:vegmove_delivery/domain/model/warehouse.dart';

class Batch {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final int warehouseId;
  final Warehouse warehouse;

  Batch({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    required this.warehouseId,
    required this.warehouse,
  });

  Batch copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    int? warehouseId,
    Warehouse? warehouse,
  }) =>
      Batch(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        warehouseId: warehouseId ?? this.warehouseId,
        warehouse: warehouse ?? this.warehouse,
      );

  factory Batch.fromJson(Map<String, dynamic> json) => Batch(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        name: json['name'],
        warehouseId: json['warehouseId'],
        warehouse: Warehouse.fromJson(json['warehouse']),
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'warehouseId': warehouseId,
        'warehouse': warehouse.toJson(),
      };
}
