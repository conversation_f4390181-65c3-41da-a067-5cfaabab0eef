// To parse this JSON data, do
//
//     final productVariation = productVariationFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/media.dart';

ProductVariation productVariationFromJson(String str) =>
    ProductVariation.fromJson(json.decode(str));

String productVariationToJson(ProductVariation data) =>
    json.encode(data.toJson());

class ProductVariation {
  final int id;
  final String barcode;
  final int productId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<VariationOption> options;
  final List<Media> media;
  final int quantity;
  final bool inStock;
  final String price;
  final String originalPrice;

  ProductVariation({
    required this.id,
    required this.barcode,
    required this.productId,
    required this.createdAt,
    required this.updatedAt,
    required this.options,
    required this.media,
    required this.quantity,
    required this.inStock,
    required this.price,
    required this.originalPrice,
  });

  ProductVariation copyWith({
    int? id,
    String? barcode,
    int? productId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<VariationOption>? options,
    List<Media>? media,
    int? quantity,
    bool? inStock,
    String? price,
    String? originalPrice,
  }) =>
      ProductVariation(
        id: id ?? this.id,
        barcode: barcode ?? this.barcode,
        productId: productId ?? this.productId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        options: options ?? this.options,
        media: media ?? this.media,
        quantity: quantity ?? this.quantity,
        inStock: inStock ?? this.inStock,
        price: price ?? this.price,
        originalPrice: originalPrice ?? this.originalPrice,
      );

  factory ProductVariation.fromJson(Map<String, dynamic> json) =>
      ProductVariation(
        id: json["id"],
        barcode: json["barcode"],
        productId: json["productId"] ?? 0,
        createdAt: json["createdAt"] != null
            ? DateTime.parse(json["createdAt"])
            : DateTime.now(),
        updatedAt: json["updatedAt"] != null
            ? DateTime.parse(json["updatedAt"])
            : DateTime.now(),
        options: json["options"] != null
            ? List<VariationOption>.from(
                json["options"].map((x) => VariationOption.fromJson(x)))
            : [],
        media: json["media"] != null
            ? List<Media>.from(json["media"].map((x) => Media.fromJson(x)))
            : [],
        quantity: json["quantity"] ?? 0,
        inStock: json["inStock"] ?? false,
        price: json["price"] ?? "0",
        originalPrice: json["originalPrice"] ?? "0",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "barcode": barcode,
        "productId": productId,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "options": List<dynamic>.from(options.map((x) => x.toJson())),
        "media": List<dynamic>.from(media.map((x) => x.toJson())),
        "quantity": quantity,
        "inStock": inStock,
        "price": price,
        "originalPrice": originalPrice,
      };
}

class VariationOption {
  final OptionWithAttribute option;

  VariationOption({
    required this.option,
  });

  VariationOption copyWith({
    OptionWithAttribute? option,
  }) =>
      VariationOption(
        option: option ?? this.option,
      );

  factory VariationOption.fromJson(Map<String, dynamic> json) =>
      VariationOption(
        option: OptionWithAttribute.fromJson(json["option"]),
      );

  Map<String, dynamic> toJson() => {
        "option": option.toJson(),
      };
}

class OptionWithAttribute {
  final int id;
  final String name;
  final String? colorCode;
  final String? imageUrl;
  final AttributeInfo attribute;

  OptionWithAttribute({
    required this.id,
    required this.name,
    this.colorCode,
    this.imageUrl,
    required this.attribute,
  });

  OptionWithAttribute copyWith({
    int? id,
    String? name,
    String? colorCode,
    String? imageUrl,
    AttributeInfo? attribute,
  }) =>
      OptionWithAttribute(
        id: id ?? this.id,
        name: name ?? this.name,
        colorCode: colorCode ?? this.colorCode,
        imageUrl: imageUrl ?? this.imageUrl,
        attribute: attribute ?? this.attribute,
      );

  factory OptionWithAttribute.fromJson(Map<String, dynamic> json) =>
      OptionWithAttribute(
        id: json["id"],
        name: json["name"],
        colorCode: json["colorCode"],
        imageUrl: json["imageUrl"],
        attribute: AttributeInfo.fromJson(json["attribute"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "colorCode": colorCode,
        "imageUrl": imageUrl,
        "attribute": attribute.toJson(),
      };
}

class AttributeInfo {
  final int id;
  final String name;

  AttributeInfo({
    required this.id,
    required this.name,
  });

  AttributeInfo copyWith({
    int? id,
    String? name,
  }) =>
      AttributeInfo(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory AttributeInfo.fromJson(Map<String, dynamic> json) => AttributeInfo(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
