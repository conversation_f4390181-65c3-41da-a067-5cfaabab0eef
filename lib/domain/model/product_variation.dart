import 'package:vegmove_delivery/domain/model/product_variation_option_mapping.dart';

class ProductVariation {
  final int id;
  final int productId;
  final String barcode;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ProductVariationOptionMapping> options;

  ProductVariation({
    required this.id,
    required this.productId,
    required this.barcode,
    required this.createdAt,
    required this.updatedAt,
    required this.options,
  });

  ProductVariation copyWith({
    int? id,
    int? productId,
    String? barcode,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ProductVariationOptionMapping>? options,
  }) =>
      ProductVariation(
        id: id ?? this.id,
        productId: productId ?? this.productId,
        barcode: barcode ?? this.barcode,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        options: options ?? this.options,
      );

  factory ProductVariation.fromJson(Map<String, dynamic> json) => ProductVariation(
        id: json['id'],
        productId: json['productId'],
        barcode: json['barcode'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        options: json['options'] != null
            ? List<ProductVariationOptionMapping>.from(
                json['options'].map((x) => ProductVariationOptionMapping.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'productId': productId,
        'barcode': barcode,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'options': List<dynamic>.from(options.map((x) => x.toJson())),
      };
}
