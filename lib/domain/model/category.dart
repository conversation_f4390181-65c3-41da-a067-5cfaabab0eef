// To parse this JSON data, do
//
//     final category = categoryFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/enums.dart';
import 'package:vegmove_warehouse/domain/model/language.dart';

List<Category> categoriesFromJson(String str) =>
    List<Category>.from(json.decode(str).map((x) => Category.fromJson(x)));

Category categoryFromJson(String str) => Category.fromJson(json.decode(str));

String categoryToJson(Category data) => json.encode(data.toJson());

class Category {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final int? parentId;
  final Category? parent;
  final CategoryType type;
  final String slug;
  final bool isActive;
  final String? bannerUrl;
  final String? iconUrl;
  final List<Category>? children;
  final List<CategoryTranslation>? categoryTranslation;
  final List<Category>? segments;
  final List<Category>? categories;

  Category({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    this.parentId,
    this.parent,
    required this.type,
    required this.slug,
    required this.isActive,
    this.bannerUrl,
    this.iconUrl,
    this.children,
    this.categoryTranslation,
    this.segments,
    this.categories,
  });

  Category copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    int? parentId,
    Category? parent,
    CategoryType? type,
    String? slug,
    bool? isActive,
    String? bannerUrl,
    String? iconUrl,
    List<Category>? children,
    List<CategoryTranslation>? categoryTranslation,
    List<Category>? segments,
    List<Category>? categories,
  }) =>
      Category(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        parentId: parentId ?? this.parentId,
        parent: parent ?? this.parent,
        type: type ?? this.type,
        slug: slug ?? this.slug,
        isActive: isActive ?? this.isActive,
        bannerUrl: bannerUrl ?? this.bannerUrl,
        iconUrl: iconUrl ?? this.iconUrl,
        children: children ?? this.children,
        categoryTranslation: categoryTranslation ?? this.categoryTranslation,
        segments: segments ?? this.segments,
        categories: categories ?? this.categories,
      );

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : DateTime.now(),
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : DateTime.now(),
        id: json['id'],
        name: json['name'],
        parentId: json['parentId'],
        parent:
            json['parent'] != null ? Category.fromJson(json['parent']) : null,
        type: CategoryType.values.byName(json['type']),
        slug: json['slug'],
        isActive: json['isActive'],
        bannerUrl: json['bannerUrl'],
        iconUrl: json['iconUrl'],
        children: json['children'] != null
            ? List<Category>.from(
                json['children'].map((x) => Category.fromJson(x)))
            : null,
        categoryTranslation: json['categoryTranslation'] != null
            ? List<CategoryTranslation>.from(json['categoryTranslation']
                .map((x) => CategoryTranslation.fromJson(x)))
            : null,
        segments: json['segments'] != null
            ? List<Category>.from(
                json['segments'].map((x) => Category.fromJson(x)))
            : null,
        categories: json['categories'] != null
            ? List<Category>.from(
                json['categories'].map((x) => Category.fromJson(x)))
            : null,
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'parentId': parentId,
        'parent': parent?.toJson(),
        'type': type,
        'slug': slug,
        'isActive': isActive,
        'bannerUrl': bannerUrl,
        'iconUrl': iconUrl,
        'children': children != null
            ? List<dynamic>.from(children!.map((x) => x.toJson()))
            : null,
        'categoryTranslation': categoryTranslation != null
            ? List<dynamic>.from(categoryTranslation!.map((x) => x.toJson()))
            : null,
        'segments': segments != null
            ? List<dynamic>.from(segments!.map((x) => x.toJson()))
            : null,
        'categories': categories != null
            ? List<dynamic>.from(categories!.map((x) => x.toJson()))
            : null,
      };
}

class CategoryTranslation {
  final int categoryId;
  final Language language;
  final int languageId;
  final String name;

  CategoryTranslation({
    required this.categoryId,
    required this.language,
    required this.languageId,
    required this.name,
  });

  CategoryTranslation copyWith({
    int? categoryId,
    Language? language,
    int? languageId,
    String? name,
  }) =>
      CategoryTranslation(
        categoryId: categoryId ?? this.categoryId,
        language: language ?? this.language,
        languageId: languageId ?? this.languageId,
        name: name ?? this.name,
      );

  factory CategoryTranslation.fromJson(Map<String, dynamic> json) =>
      CategoryTranslation(
        categoryId: json['categoryId'],
        language: Language.fromJson(json['language']),
        languageId: json['languageId'],
        name: json['name'],
      );

  Map<String, dynamic> toJson() => {
        'categoryId': categoryId,
        'language': language.toJson(),
        'languageId': languageId,
        'name': name,
      };
}
