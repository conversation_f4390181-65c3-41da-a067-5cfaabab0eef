import 'dart:convert';

PaginationData paginationDataFromJson(String str) =>
    PaginationData.fromJson(json.decode(str));

String paginationDataToJson(PaginationData data) => json.encode(data.toJson());

class PaginationData {
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginationData({
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  PaginationData copyWith({
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      PaginationData(
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory PaginationData.fromJson(Map<String, dynamic> json) => PaginationData(
        total: json["total"],
        page: json["page"],
        pageSize: json["pageSize"],
        totalPages: json["totalPages"],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "page": page,
        "pageSize": pageSize,
        "totalPages": totalPages,
      };
}
