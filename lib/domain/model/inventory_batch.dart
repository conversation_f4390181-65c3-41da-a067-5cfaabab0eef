import 'package:vegmove_warehouse/domain/model/warehouse.dart';

class InventoryBatch {
  final int id;
  final String name;
  final int? warehouseId;
  final Warehouse warehouse;
  final DateTime createdAt;
  final DateTime updatedAt;

  InventoryBatch({
    required this.id,
    required this.name,
    this.warehouseId,
    required this.warehouse,
    required this.createdAt,
    required this.updatedAt,
  });

  InventoryBatch copyWith({
    int? id,
    String? name,
    int? warehouseId,
    Warehouse? warehouse,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      InventoryBatch(
        id: id ?? this.id,
        name: name ?? this.name,
        warehouseId: warehouseId ?? this.warehouseId,
        warehouse: warehouse ?? this.warehouse,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory InventoryBatch.fromJson(Map<String, dynamic> json) {
    return InventoryBatch(
      id: json['id'],
      name: json['name'],
      warehouseId: json['warehouseId'],
      warehouse: Warehouse.fromJson(json['warehouse']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'warehouseId': warehouseId,
        'warehouse': warehouse.toJson(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };
}
