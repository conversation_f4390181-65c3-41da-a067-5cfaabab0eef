import 'package:vegmove_delivery/domain/model/batch.dart';

class Inventory {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int productId;
  final String buyingPrice;
  final String sellingPrice;
  final dynamic manufactureDate;
  final dynamic expiryDate;
  final dynamic supplierId;
  final int batchId;
  final Batch batch;

  Inventory({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.productId,
    required this.buyingPrice,
    required this.sellingPrice,
    required this.manufactureDate,
    required this.expiryDate,
    required this.supplierId,
    required this.batchId,
    required this.batch,
  });

  Inventory copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? productId,
    String? buyingPrice,
    String? sellingPrice,
    dynamic manufactureDate,
    dynamic expiryDate,
    dynamic supplierId,
    int? batchId,
    Batch? batch,
  }) =>
      Inventory(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        productId: productId ?? this.productId,
        buyingPrice: buyingPrice ?? this.buyingPrice,
        sellingPrice: sellingPrice ?? this.sellingPrice,
        manufactureDate: manufactureDate ?? this.manufactureDate,
        expiryDate: expiryDate ?? this.expiryDate,
        supplierId: supplierId ?? this.supplierId,
        batchId: batchId ?? this.batchId,
        batch: batch ?? this.batch,
      );

  factory Inventory.fromJson(Map<String, dynamic> json) => Inventory(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        productId: json['productId'],
        buyingPrice: json['buyingPrice'],
        sellingPrice: json['sellingPrice'],
        manufactureDate: json['manufactureDate'],
        expiryDate: json['expiryDate'],
        supplierId: json['supplierId'],
        batchId: json['batchId'],
        batch: Batch.fromJson(json['batch']),
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'productId': productId,
        'buyingPrice': buyingPrice,
        'sellingPrice': sellingPrice,
        'manufactureDate': manufactureDate,
        'expiryDate': expiryDate,
        'supplierId': supplierId,
        'batchId': batchId,
        'batch': batch.toJson(),
      };
}
