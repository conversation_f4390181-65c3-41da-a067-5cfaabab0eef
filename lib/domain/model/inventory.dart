// To parse this JSON data, do
//
//     final inventory = inventoryFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/inventory_batch.dart';

Inventory inventoryFromJson(String str) => Inventory.fromJson(json.decode(str));

String inventoryToJson(Inventory data) => json.encode(data.toJson());

class Inventory {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int productId;
  final String buyingPrice;
  final String sellingPrice;
  final DateTime? manufactureDate;
  final DateTime? expiryDate;
  final int? supplierId;
  final int batchId;
  final InventoryBatch batch;
  final int quantity;
  final int? variationId;

  Inventory({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.productId,
    required this.buyingPrice,
    required this.sellingPrice,
    this.manufactureDate,
    this.expiryDate,
    this.supplierId,
    required this.batchId,
    required this.batch,
    required this.quantity,
    this.variationId,
  });

  Inventory copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? productId,
    String? buyingPrice,
    String? sellingPrice,
    DateTime? manufactureDate,
    DateTime? expiryDate,
    int? supplierId,
    int? batchId,
    InventoryBatch? batch,
    int? quantity,
    int? variationId,
  }) =>
      Inventory(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        productId: productId ?? this.productId,
        buyingPrice: buyingPrice ?? this.buyingPrice,
        sellingPrice: sellingPrice ?? this.sellingPrice,
        manufactureDate: manufactureDate ?? this.manufactureDate,
        expiryDate: expiryDate ?? this.expiryDate,
        supplierId: supplierId ?? this.supplierId,
        batchId: batchId ?? this.batchId,
        batch: batch ?? this.batch,
        quantity: quantity ?? this.quantity,
        variationId: variationId ?? this.variationId,
      );

  factory Inventory.fromJson(Map<String, dynamic> json) => Inventory(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        productId: json["productId"],
        buyingPrice: json["buyingPrice"],
        sellingPrice: json["sellingPrice"],
        manufactureDate: json["manufactureDate"] != null
            ? DateTime.parse(json["manufactureDate"])
            : null,
        expiryDate: json["expiryDate"] != null
            ? DateTime.parse(json["expiryDate"])
            : null,
        supplierId: json["supplierId"],
        batchId: json["batchId"],
        batch: InventoryBatch.fromJson(json["batch"]),
        quantity: json["quantity"] ?? 0,
        variationId: json["variationId"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "productId": productId,
        "buyingPrice": buyingPrice,
        "sellingPrice": sellingPrice,
        "manufactureDate": manufactureDate?.toIso8601String(),
        "expiryDate": expiryDate?.toIso8601String(),
        "supplierId": supplierId,
        "batchId": batchId,
        "batch": batch.toJson(),
        "quantity": quantity,
        "variationId": variationId,
      };
}
