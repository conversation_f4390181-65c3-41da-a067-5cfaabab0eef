import 'dart:convert';

DistanceFromWarehouse distanceFromWarehouseFromJson(String str) =>
    DistanceFromWarehouse.fromJson(json.decode(str));

String distanceFromWarehouseToJson(DistanceFromWarehouse data) =>
    json.encode(data.toJson());

class DistanceFromWarehouse {
  final double distance;
  final String unit;
  final DateTime lastUpdated;

  DistanceFromWarehouse({
    required this.distance,
    required this.unit,
    required this.lastUpdated,
  });

  DistanceFromWarehouse copyWith({
    double? distance,
    String? unit,
    DateTime? lastUpdated,
  }) =>
      DistanceFromWarehouse(
        distance: distance ?? this.distance,
        unit: unit ?? this.unit,
        lastUpdated: lastUpdated ?? this.lastUpdated,
      );

  factory DistanceFromWarehouse.fromJson(Map<String, dynamic> json) =>
      DistanceFromWarehouse(
        distance: json["distance"]?.toDouble() ?? 0.0,
        unit: json["unit"] ?? "km",
        lastUpdated: DateTime.parse(json["lastUpdated"]),
      );

  Map<String, dynamic> toJson() => {
        "distance": distance,
        "unit": unit,
        "lastUpdated": lastUpdated.toIso8601String(),
      };
}
