import 'package:vegmove_delivery/domain/model/product_attribute.dart';

class ProductAttributeOption {
  final int id;
  final String name;
  final String? colorCode;
  final String? imageUrl;
  final String? iconUrl;
  final ProductAttribute attribute;

  ProductAttributeOption({
    required this.id,
    required this.name,
    this.colorCode,
    this.imageUrl,
    this.iconUrl,
    required this.attribute,
  });

  ProductAttributeOption copyWith({
    int? id,
    String? name,
    String? colorCode,
    String? imageUrl,
    String? iconUrl,
    ProductAttribute? attribute,
  }) =>
      ProductAttributeOption(
        id: id ?? this.id,
        name: name ?? this.name,
        colorCode: colorCode ?? this.colorCode,
        imageUrl: imageUrl ?? this.imageUrl,
        iconUrl: iconUrl ?? this.iconUrl,
        attribute: attribute ?? this.attribute,
      );

  factory ProductAttributeOption.fromJson(Map<String, dynamic> json) => ProductAttributeOption(
        id: json['id'],
        name: json['name'],
        colorCode: json['colorCode'],
        imageUrl: json['imageUrl'],
        iconUrl: json['iconUrl'],
        attribute: ProductAttribute.fromJson(json['attribute']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'colorCode': colorCode,
        'imageUrl': imageUrl,
        'iconUrl': iconUrl,
        'attribute': attribute.toJson(),
      };
}
