import 'package:vegmove_delivery/domain/model/product_attribute_option.dart';

class ProductVariationOptionMapping {
  final int variationId;
  final int optionId;
  final ProductAttributeOption option;

  ProductVariationOptionMapping({
    required this.variationId,
    required this.optionId,
    required this.option,
  });

  ProductVariationOptionMapping copyWith({
    int? variationId,
    int? optionId,
    ProductAttributeOption? option,
  }) =>
      ProductVariationOptionMapping(
        variationId: variationId ?? this.variationId,
        optionId: optionId ?? this.optionId,
        option: option ?? this.option,
      );

  factory ProductVariationOptionMapping.fromJson(Map<String, dynamic> json) => ProductVariationOptionMapping(
        variationId: json['variationId'] ?? 0,
        optionId: json['optionId'] ?? 0,
        option: ProductAttributeOption.fromJson(json['option']),
      );

  Map<String, dynamic> toJson() => {
        'variationId': variationId,
        'optionId': optionId,
        'option': option.toJson(),
      };
}
