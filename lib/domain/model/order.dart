// To parse this JSON data, do
//
//     final order = orderFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_warehouse/domain/model/address.dart';
import 'package:vegmove_warehouse/domain/model/enums.dart';
import 'package:vegmove_warehouse/domain/model/order_item.dart';
import 'package:vegmove_warehouse/domain/model/order_status_history.dart';
import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/domain/model/warehouse.dart';

Order orderFromJson(String str) => Order.fromJson(json.decode(str));

String orderToJson(Order data) => json.encode(data.toJson());

class Order {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int userId;
  final OrderStatus status;
  final int addressId;
  final String subtotal;
  final String handlingCharge;
  final String deliveryFee;
  final String discountAmount;
  final String couponDiscount;
  final String rewardDiscount;
  final String totalAmount;
  final DateTime deliveryDate;
  final String deliveryStartTime;
  final String deliveryEndTime;
  final String? note;
  final String cashToPay;
  final int? couponId;
  final int? deliveryDriverId;
  final bool useRewardPoints;
  final int rewardPointsUsed;
  final int? warehouseId;
  final Warehouse? warehouse;
  final DateTime? deliveryDriverAssignedDate;
  final PaymentStatus paymentStatus;
  final List<OrderItem> items;
  final Address address;
  final User? user;
  final List<OrderStatusHistory> orderStatusHistory;
  final User? deliveryDriver;

  Order({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.userId,
    required this.status,
    required this.addressId,
    required this.subtotal,
    required this.handlingCharge,
    required this.deliveryFee,
    required this.discountAmount,
    required this.couponDiscount,
    required this.rewardDiscount,
    required this.totalAmount,
    required this.deliveryDate,
    required this.deliveryStartTime,
    required this.deliveryEndTime,
    this.note,
    required this.cashToPay,
    this.couponId,
    this.deliveryDriverId,
    required this.useRewardPoints,
    required this.rewardPointsUsed,
    this.warehouseId,
    this.deliveryDriverAssignedDate,
    required this.paymentStatus,
    required this.items,
    required this.address,
    this.user,
    required this.orderStatusHistory,
    this.warehouse,
    this.deliveryDriver,
  });

  Order copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? userId,
    OrderStatus? status,
    int? addressId,
    String? subtotal,
    String? handlingCharge,
    String? deliveryFee,
    String? discountAmount,
    String? couponDiscount,
    String? rewardDiscount,
    String? totalAmount,
    DateTime? deliveryDate,
    String? deliveryTime,
    String? deliveryStartTime,
    String? deliveryEndTime,
    String? note,
    String? cashToPay,
    int? couponId,
    int? deliveryDriverId,
    bool? useRewardPoints,
    int? rewardPointsUsed,
    int? warehouseId,
    DateTime? deliveryDriverAssignedDate,
    PaymentStatus? paymentStatus,
    List<OrderItem>? items,
    Address? address,
    User? user,
    List<OrderStatusHistory>? orderStatusHistory,
    Warehouse? warehouse,
    User? deliveryDriver,
  }) =>
      Order(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        userId: userId ?? this.userId,
        status: status ?? this.status,
        addressId: addressId ?? this.addressId,
        subtotal: subtotal ?? this.subtotal,
        handlingCharge: handlingCharge ?? this.handlingCharge,
        deliveryFee: deliveryFee ?? this.deliveryFee,
        discountAmount: discountAmount ?? this.discountAmount,
        couponDiscount: couponDiscount ?? this.couponDiscount,
        rewardDiscount: rewardDiscount ?? this.rewardDiscount,
        totalAmount: totalAmount ?? this.totalAmount,
        deliveryDate: deliveryDate ?? this.deliveryDate,
        deliveryStartTime: deliveryStartTime ?? this.deliveryStartTime,
        deliveryEndTime: deliveryEndTime ?? this.deliveryEndTime,
        note: note ?? this.note,
        cashToPay: cashToPay ?? this.cashToPay,
        couponId: couponId ?? this.couponId,
        deliveryDriverId: deliveryDriverId ?? this.deliveryDriverId,
        useRewardPoints: useRewardPoints ?? this.useRewardPoints,
        rewardPointsUsed: rewardPointsUsed ?? this.rewardPointsUsed,
        warehouseId: warehouseId ?? this.warehouseId,
        deliveryDriverAssignedDate:
            deliveryDriverAssignedDate ?? this.deliveryDriverAssignedDate,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        items: items ?? this.items,
        address: address ?? this.address,
        user: user ?? this.user,
        orderStatusHistory: orderStatusHistory ?? this.orderStatusHistory,
        warehouse: warehouse ?? this.warehouse,
        deliveryDriver: deliveryDriver ?? this.deliveryDriver,
      );

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        userId: json['userId'],
        status: OrderStatus.values.byName(json['status']),
        addressId: json['addressId'],
        subtotal: json['subtotal'] ?? '0',
        handlingCharge: json['handlingCharge'] ?? '0',
        deliveryFee: json['deliveryFee'] ?? '0',
        discountAmount: json['discountAmount'] ?? '0',
        couponDiscount: json['couponDiscount'] ?? '0',
        rewardDiscount: json['rewardDiscount'] ?? '0',
        totalAmount: json['totalAmount'],
        deliveryDate: DateTime.parse(json['deliveryDate']),
        deliveryStartTime: json['deliveryStartTime'] ?? '',
        deliveryEndTime: json['deliveryEndTime'] ?? '',
        note: json['note'],
        cashToPay: json['cashToPay'] ?? '0',
        couponId: json['couponId'],
        deliveryDriverId: json['deliveryDriverId'],
        useRewardPoints: json['useRewardPoints'] ?? false,
        rewardPointsUsed: json['rewardPointsUsed'] ?? 0,
        warehouseId: json['warehouseId'],
        deliveryDriverAssignedDate: json['deliveryDriverAssignedDate'] != null
            ? DateTime.parse(json['deliveryDriverAssignedDate'])
            : null,
        paymentStatus: PaymentStatus.values.byName(json['paymentStatus']),
        items: List<OrderItem>.from(
            json['items'].map((x) => OrderItem.fromJson(x))),
        address: Address.fromJson(json['address']),
        user: json['user'] != null ? User.fromJson(json['user']) : null,
        orderStatusHistory: List<OrderStatusHistory>.from(
            json['orderStatusHistory']
                .map((x) => OrderStatusHistory.fromJson(x))),
        warehouse: json['warehouse'] != null
            ? Warehouse.fromJson(json['warehouse'])
            : null,
        deliveryDriver: json['deliveryDriver'] != null
            ? User.fromJson(json['deliveryDriver'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'userId': userId,
        'status': status.name,
        'addressId': addressId,
        'subtotal': subtotal,
        'handlingCharge': handlingCharge,
        'deliveryFee': deliveryFee,
        'discountAmount': discountAmount,
        'couponDiscount': couponDiscount,
        'rewardDiscount': rewardDiscount,
        'totalAmount': totalAmount,
        'deliveryDate': deliveryDate.toIso8601String(),
        'deliveryStartTime': deliveryStartTime,
        'deliveryEndTime': deliveryEndTime,
        'note': note,
        'cashToPay': cashToPay,
        'couponId': couponId,
        'deliveryDriverId': deliveryDriverId,
        'useRewardPoints': useRewardPoints,
        'rewardPointsUsed': rewardPointsUsed,
        'warehouseId': warehouseId,
        'deliveryDriverAssignedDate':
            deliveryDriverAssignedDate?.toIso8601String(),
        'paymentStatus': paymentStatus.name,
        'items': List<dynamic>.from(items.map((x) => x.toJson())),
        'address': address.toJson(),
        'user': user?.toJson(),
        'orderStatusHistory':
            List<dynamic>.from(orderStatusHistory.map((x) => x.toJson())),
        'deliveryDriver': deliveryDriver?.toJson(),
      };
}
