import 'dart:convert';

import 'package:vegmove_delivery/domain/model/address.dart';
import 'package:vegmove_delivery/domain/model/order_item.dart';
import 'package:vegmove_delivery/domain/model/order_status_history.dart';
import 'package:vegmove_delivery/domain/model/user.dart';

Order orderFromJson(String str) => Order.fromJson(json.decode(str));

class Order {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int userId;
  final String status;
  final int addressId;
  final String totalAmount;
  final String discountAmount;
  final DateTime deliveryDate;
  final String deliveryStartTime;
  final String deliveryEndTime;
  final String note;
  final dynamic couponId;
  final int deliveryDriverId;
  final String paymentStatus;
  final List<Item> items;
  final Address address;
  final User user;
  final List<OrderStatusHistory> orderStatusHistory;
  final DateTime? deliveryDeadline;
  final DateTime? pickupDeadline;
  final int? travelTime;
  final String cashToPay;

  Order({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.userId,
    required this.status,
    required this.addressId,
    required this.totalAmount,
    required this.discountAmount,
    required this.deliveryDate,
    required this.deliveryStartTime,
    required this.deliveryEndTime,
    required this.note,
    required this.couponId,
    required this.deliveryDriverId,
    required this.paymentStatus,
    required this.items,
    required this.address,
    required this.user,
    required this.orderStatusHistory,
    this.deliveryDeadline,
    this.pickupDeadline,
    this.travelTime,
    this.cashToPay = '0',
  });

  Order copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? userId,
    String? status,
    int? addressId,
    String? totalAmount,
    String? discountAmount,
    DateTime? deliveryDate,
    String? deliveryStartTime,
    String? deliveryEndTime,
    String? note,
    dynamic couponId,
    int? deliveryDriverId,
    String? paymentStatus,
    List<Item>? items,
    Address? address,
    User? user,
    List<OrderStatusHistory>? orderStatusHistory,
    DateTime? deliveryDeadline,
    DateTime? pickupDeadline,
    int? travelTime,
    String? cashToPay,
  }) =>
      Order(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        userId: userId ?? this.userId,
        status: status ?? this.status,
        addressId: addressId ?? this.addressId,
        totalAmount: totalAmount ?? this.totalAmount,
        discountAmount: discountAmount ?? this.discountAmount,
        deliveryDate: deliveryDate ?? this.deliveryDate,
        deliveryStartTime: deliveryStartTime ?? this.deliveryStartTime,
        deliveryEndTime: deliveryEndTime ?? this.deliveryEndTime,
        note: note ?? this.note,
        couponId: couponId ?? this.couponId,
        deliveryDriverId: deliveryDriverId ?? this.deliveryDriverId,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        items: items ?? this.items,
        address: address ?? this.address,
        user: user ?? this.user,
        orderStatusHistory: orderStatusHistory ?? this.orderStatusHistory,
        deliveryDeadline: deliveryDeadline ?? this.deliveryDeadline,
        pickupDeadline: pickupDeadline ?? this.pickupDeadline,
        travelTime: travelTime ?? this.travelTime,
        cashToPay: cashToPay ?? this.cashToPay,
      );

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        userId: json['userId'],
        status: json['status'],
        addressId: json['addressId'],
        totalAmount: json['totalAmount'],
        discountAmount: json['discountAmount'],
        deliveryDate: DateTime.parse(json['deliveryDate']),
        deliveryStartTime: json['deliveryStartTime'],
        deliveryEndTime: json['deliveryEndTime'],
        note: json['note'],
        couponId: json['couponId'],
        deliveryDriverId: json['deliveryDriverId'],
        paymentStatus: json['paymentStatus'],
        items: List<Item>.from(json['items'].map((x) => Item.fromJson(x))),
        address: Address.fromJson(json['address']),
        user: User.fromJson(json['user']),
        orderStatusHistory: List<OrderStatusHistory>.from(
            json['orderStatusHistory']
                .map((x) => OrderStatusHistory.fromJson(x))),
        deliveryDeadline: json['deliveryDeadline'] != null
            ? DateTime.parse(json['deliveryDeadline'])
            : null,
        pickupDeadline: json['pickupDeadline'] != null
            ? DateTime.parse(json['pickupDeadline'])
            : null,
        travelTime: json['travelTime'],
        cashToPay: json['cashToPay'] ?? '0',
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'userId': userId,
        'status': status,
        'addressId': addressId,
        'totalAmount': totalAmount,
        'discountAmount': discountAmount,
        'deliveryDate': deliveryDate.toIso8601String(),
        'deliveryStartTime': deliveryStartTime,
        'deliveryEndTime': deliveryEndTime,
        'note': note,
        'couponId': couponId,
        'deliveryDriverId': deliveryDriverId,
        'paymentStatus': paymentStatus,
        'items': List<dynamic>.from(items.map((x) => x.toJson())),
        'address': address.toJson(),
        'user': user.toJson(),
        'orderStatusHistory':
            List<dynamic>.from(orderStatusHistory.map((x) => x.toJson())),
        'deliveryDeadline': deliveryDeadline?.toIso8601String(),
        'pickupDeadline': pickupDeadline?.toIso8601String(),
        'travelTime': travelTime,
        'cashToPay': cashToPay,
      };
}
