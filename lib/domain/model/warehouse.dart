// To parse this JSON data, do
//
//     final warehouse = warehouseFromJson(jsonString);

import 'dart:convert';

Warehouse warehouseFromJson(String str) => Warehouse.fromJson(json.decode(str));

String warehouseToJson(Warehouse data) => json.encode(data.toJson());

enum WarehouseType { GENERAL, SUPER }

class Warehouse {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final String lat;
  final String long;
  final WarehouseType type;
  final bool active;

  Warehouse({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    required this.lat,
    required this.long,
    required this.type,
    required this.active,
  });

  Warehouse copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    String? lat,
    String? long,
    WarehouseType? type,
    bool? active,
  }) =>
      Warehouse(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        lat: lat ?? this.lat,
        long: long ?? this.long,
        type: type ?? this.type,
        active: active ?? this.active,
      );

  factory Warehouse.fromJson(Map<String, dynamic> json) => Warehouse(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        name: json["name"],
        lat: json["lat"],
        long: json["long"],
        type: WarehouseType.values.byName(json["type"]),
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "name": name,
        "lat": lat,
        "long": long,
        "type": type.name,
        "active": active,
      };
}
