class Warehouse {
  final int id;
  final String name;
  final String lat;
  final String long;
  final String type;
  final bool active;
  final DateTime createdAt;
  final DateTime updatedAt;

  Warehouse({
    required this.id,
    required this.name,
    required this.lat,
    required this.long,
    required this.type,
    required this.active,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Warehouse.fromJson(Map<String, dynamic> json) {
    return Warehouse(
      id: json['id'],
      name: json['name'],
      lat: json['lat'],
      long: json['long'],
      type: json['type'],
      active: json['active'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'lat': lat,
        'long': long,
        'type': type,
        'active': active,
      };
}
