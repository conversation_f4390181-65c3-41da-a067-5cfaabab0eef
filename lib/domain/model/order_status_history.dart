// To parse this JSON data, do
//
//     final orderStatusHistory = orderStatusHistoryFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_warehouse/domain/model/enums.dart';

OrderStatusHistory orderStatusHistoryFromJson(String str) =>
    OrderStatusHistory.fromJson(json.decode(str));

String orderStatusHistoryToJson(OrderStatusHistory data) =>
    json.encode(data.toJson());

class OrderStatusHistory {
  final DateTime createdAt;
  final int id;
  final int orderId;
  final OrderStatus status;

  OrderStatusHistory({
    required this.createdAt,
    required this.id,
    required this.orderId,
    required this.status,
  });

  OrderStatusHistory copyWith({
    DateTime? createdAt,
    int? id,
    int? orderId,
    OrderStatus? status,
  }) =>
      OrderStatusHistory(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        orderId: orderId ?? this.orderId,
        status: status ?? this.status,
      );

  factory OrderStatusHistory.fromJson(Map<String, dynamic> json) =>
      OrderStatusHistory(
        createdAt: DateTime.parse(json["createdAt"]),
        id: json["id"],
        orderId: json["orderId"],
        status: OrderStatus.values.byName(json["status"]),
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "id": id,
        "orderId": orderId,
        "status": status.name,
      };
}
