class OrderStatusHistory {
  final DateTime createdAt;
  final int id;
  final int orderId;
  final String status;

  OrderStatusHistory({
    required this.createdAt,
    required this.id,
    required this.orderId,
    required this.status,
  });

  OrderStatusHistory copyWith({
    DateTime? createdAt,
    int? id,
    int? orderId,
    String? status,
  }) =>
      OrderStatusHistory(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        orderId: orderId ?? this.orderId,
        status: status ?? this.status,
      );

  factory OrderStatusHistory.fromJson(Map<String, dynamic> json) =>
      OrderStatusHistory(
        createdAt: DateTime.parse(json['createdAt']),
        id: json['id'],
        orderId: json['orderId'],
        status: json['status'],
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'id': id,
        'orderId': orderId,
        'status': status,
      };
}
