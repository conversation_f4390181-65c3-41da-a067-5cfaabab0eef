// To parse this JSON data, do
//
//     final supplier = supplierFromJson(jsonString);

import 'dart:convert';

List<Supplier> supplierFromJson(String str) =>
    List<Supplier>.from(json.decode(str).map((x) => Supplier.fromJson(x)));

String supplierToJson(List<Supplier> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Supplier {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final int countryId;
  final Country country;

  Supplier({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.countryId,
    required this.country,
  });

  Supplier copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    int? countryId,
    Country? country,
  }) =>
      Supplier(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        address: address ?? this.address,
        countryId: countryId ?? this.countryId,
        country: country ?? this.country,
      );

  factory Supplier.fromJson(Map<String, dynamic> json) => Supplier(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        name: json['name'],
        email: json['email'],
        phone: json['phone'],
        address: json['address'],
        countryId: json['countryId'],
        country: Country.fromJson(json['country']),
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'email': email,
        'phone': phone,
        'address': address,
        'countryId': countryId,
        'country': country.toJson(),
      };
}

class Country {
  final int id;
  final String name;
  final String code;
  final String dialCode;
  final String flagUrl;

  Country({
    required this.id,
    required this.name,
    required this.code,
    required this.dialCode,
    required this.flagUrl,
  });

  Country copyWith({
    int? id,
    String? name,
    String? code,
    String? dialCode,
    String? flagUrl,
  }) =>
      Country(
        id: id ?? this.id,
        name: name ?? this.name,
        code: code ?? this.code,
        dialCode: dialCode ?? this.dialCode,
        flagUrl: flagUrl ?? this.flagUrl,
      );

  factory Country.fromJson(Map<String, dynamic> json) => Country(
        id: json['id'],
        name: json['name'],
        code: json['code'],
        dialCode: json['dialCode'],
        flagUrl: json['flagUrl'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'code': code,
        'dialCode': dialCode,
        'flagUrl': flagUrl,
      };
}
