// To parse this JSON data, do
//
//     final user = userFrom<PERSON><PERSON>(jsonString);

import 'dart:convert';

import 'package:vegmove_delivery/domain/model/country.dart';

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToJson(User data) => json.encode(data.toJson());

class User {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final Country phoneCountry;
  final dynamic profilePicture;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String type;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.phoneCountry,
    required this.profilePicture,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
  });

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    Country? phoneCountry,
    dynamic profilePicture,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? type,
  }) =>
      User(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        phoneCountry: phoneCountry ?? this.phoneCountry,
        profilePicture: profilePicture ?? this.profilePicture,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        type: type ?? this.type,
      );

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'],
        name: json['name'],
        email: json['email'],
        phone: json['phone'],
        phoneCountry: Country.fromJson(json['phoneCountry']),
        profilePicture: json['profilePicture'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        type: json['type'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'email': email,
        'phone': phone,
        'phoneCountry': phoneCountry.toJson(),
        'profilePicture': profilePicture,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'type': type,
      };
}
