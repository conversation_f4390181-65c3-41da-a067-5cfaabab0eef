import 'dart:convert';
import 'dart:developer';

import 'package:vegmove_delivery/domain/model/delivery_driver_location.dart';
import 'package:vegmove_delivery/network/api.dart';
import 'package:vegmove_delivery/network/dto/update_location_dto.dart';

class LocationService {
  final Api _api = Api();

  /// Update the driver's location
  Future<DeliveryDriverLocation?> updateLocation({
    required String lat,
    required String long,
  }) async {
    try {
      final data = UpdateLocationDto(lat: lat, long: long);

      final response = await _api.post(
        'delivery-drivers/location',
        data: data.toJson(),
      );

      if (response == null) {
        return null;
      }

      final jsonData = jsonDecode(response);
      return DeliveryDriverLocation.fromJson(jsonData);
    } catch (e, stacktrace) {
      log('Error updating location: $e');
      log(stacktrace.toString());
      return null;
    }
  }
}
