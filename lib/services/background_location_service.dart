import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_delivery/services/location_service.dart';

// Background location service
class BackgroundLocationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  static final LocationService _locationService = LocationService();

  // Notification channel ID
  static const String notificationChannelId = 'location_service';
  static const int notificationId = 888;

  // Initialize the background service
  static Future<void> init() async {
    // Create notification channel
    await _createNotificationChannel();

    // Initialize background service
    final service = FlutterBackgroundService();

    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: _onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: notificationChannelId,
        initialNotificationTitle: 'Vegmove Delivery',
        initialNotificationContent: 'Tracking location in background',
        foregroundServiceNotificationId: notificationId,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: _onStart,
        onBackground: _onIosBackground,
      ),
    );
  }

  // Create notification channel for Android
  static Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      notificationChannelId, // id
      'Location Tracking Service', // title
      description:
          'This channel is used for location tracking service notifications.', // description
      importance: Importance.low, // importance must be at low or higher level
    );

    // Create the notification channel
    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    // Initialize notification settings
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
    );

    await _notificationsPlugin.initialize(settings);
  }

  // Start the background service
  static Future<void> startService({bool hasShippingOrder = false}) async {
    // Save update interval configuration to shared preferences
    final prefs = await SharedPreferences.getInstance();

    // Set default intervals if not already set
    if (!prefs.containsKey('normal_interval_seconds')) {
      await prefs.setInt('normal_interval_seconds', 30);
    }
    if (!prefs.containsKey('shipping_interval_seconds')) {
      await prefs.setInt('shipping_interval_seconds', 10);
    }

    // Start the service
    final service = FlutterBackgroundService();
    await service.startService();
  }

  // Stop the background service
  static Future<void> stopService() async {
    final service = FlutterBackgroundService();
    service.invoke('stopService');
  }

  // Update shipping order status
  static Future<void> updateShippingOrderStatus(bool hasShippingOrder) async {
    final service = FlutterBackgroundService();
    service.invoke(
      'updateShippingStatus',
      {'hasShippingOrder': hasShippingOrder},
    );
  }

  // Background service entry point
  @pragma('vm:entry-point')
  static Future<void> _onStart(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();

    // For Android, we need to set up a foreground notification
    if (service is AndroidServiceInstance) {
      // Create the foreground notification
      await service.setForegroundNotificationInfo(
        title: 'Vegmove Delivery',
        content: 'Location tracking is active',
      );

      // Set as foreground service
      await service.setAsForegroundService();
    }

    // Load configuration from shared preferences
    final prefs = await SharedPreferences.getInstance();
    int normalInterval = prefs.getInt('normal_interval_seconds') ?? 30;
    int shippingInterval = prefs.getInt('shipping_interval_seconds') ?? 10;

    // Default to normal interval
    int intervalSeconds = normalInterval;
    bool isShippingOrder = false;
    Timer? timer;

    // Handle service commands
    service.on('stopService').listen((event) {
      timer?.cancel();
      service.stopSelf();
    });

    service.on('updateShippingStatus').listen((event) {
      if (event != null) {
        final Map<dynamic, dynamic> data = event as Map<dynamic, dynamic>;
        isShippingOrder = data['hasShippingOrder'] ?? false;
        intervalSeconds = isShippingOrder ? shippingInterval : normalInterval;

        log('Location tracking interval updated: $intervalSeconds seconds (shipping: $isShippingOrder)');

        // Restart timer with new interval
        timer?.cancel();
        timer = Timer.periodic(
          Duration(seconds: intervalSeconds),
          (_) => _updateLocation(service),
        );
      }
    });

    // Start periodic location updates
    timer = Timer.periodic(
      Duration(seconds: intervalSeconds),
      (_) => _updateLocation(service),
    );

    // Initial location update
    await _updateLocation(service);
  }

  // Update location and send to server
  static Future<void> _updateLocation(ServiceInstance service) async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log('Location services are disabled');
        return;
      }

      // Check for location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        log('Location permission denied');
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Send location to server
      final location = await _locationService.updateLocation(
        lat: position.latitude.toString(),
        long: position.longitude.toString(),
      );

      if (location != null) {
        // Save last location to shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setDouble('last_location_lat', position.latitude);
        await prefs.setDouble('last_location_lng', position.longitude);
        await prefs.setString(
            'last_location_time', DateTime.now().toIso8601String());

        // Update notification with current status
        if (service is AndroidServiceInstance) {
          final updateTime = DateTime.now().toString().substring(11, 19);

          service.setForegroundNotificationInfo(
            title: 'Vegmove Delivery',
            content: 'Location updated at $updateTime',
          );
        }

        log('Location updated: ${position.latitude}, ${position.longitude}');
      }
    } catch (e) {
      log('Error updating location in background: $e');
    }
  }

  // iOS background handler
  @pragma('vm:entry-point')
  static Future<bool> _onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    return true;
  }
}
