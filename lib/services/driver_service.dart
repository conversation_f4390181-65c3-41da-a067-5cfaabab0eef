import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:vegmove_delivery/domain/model/delivery_driver_profile.dart';
import 'package:vegmove_delivery/network/api.dart';
import 'package:vegmove_delivery/network/dto/update_status_dto.dart';

class DriverService {
  final Api _api = Api();

  /// Get the driver's profile including active status
  Future<DeliveryDriverProfile?> getDriverProfile() async {
    try {
      final response = await _api.get('delivery-drivers/profile');

      if (response == null) {
        return null;
      }

      final jsonData = jsonDecode(response);
      return DeliveryDriverProfile.fromJson(jsonData);
    } catch (e, stacktrace) {
      log('Error getting driver profile: $e');
      log(stacktrace.toString());
      debugPrint('Error getting driver profile: $e');
      debugPrint(stacktrace.toString());
      return null;
    }
  }

  /// Update the driver's active status
  Future<DeliveryDriverProfile?> updateDriverStatus({
    required bool isActive,
  }) async {
    try {
      final data = UpdateStatusDto(isActive: isActive);

      final response = await _api.patch(
        'delivery-drivers/status',
        data: data.toJson(),
      );

      if (response == null) {
        return null;
      }

      final jsonData = jsonDecode(response);
      return DeliveryDriverProfile.fromJson(jsonData);
    } catch (e, stacktrace) {
      log('Error updating driver status: $e');
      log(stacktrace.toString());
      return null;
    }
  }
}
