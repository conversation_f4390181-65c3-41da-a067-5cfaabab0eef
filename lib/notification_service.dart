import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _plugin =
      FlutterLocalNotificationsPlugin();

  // Store the latest notification data
  static Map<String, dynamic>? latestNotificationData;

  static Future<void> init() async {
    const androidInit = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidInit);

    await _plugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        if (latestNotificationData != null) {
          // Navigation will be handled in the main.dart file
          print('Notification tapped: $latestNotificationData');
        }
      },
    );
  }

  static Future<void> show(RemoteMessage message) async {
    final notification = message.notification;
    final android = notification?.android;

    // Store the data for later use when notification is tapped
    if (message.data.isNotEmpty) {
      latestNotificationData = message.data;
    }

    if (notification != null && android != null) {
      const androidDetails = AndroidNotificationDetails(
        'default_channel',
        'General Notifications',
        channelDescription: 'For showing all notifications',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );

      const platformDetails = NotificationDetails(android: androidDetails);

      await _plugin.show(
        notification.hashCode,
        notification.title,
        notification.body,
        platformDetails,
        payload: message.data.toString(),
      );
    }
  }
}
