import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/providers/category_provider.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/components/warehouse_switch.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CategoriesScreen extends ConsumerWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: VegmoveAppBar(),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          return ref.refresh(categoriesProvider.future);
        },
        child: ListView(
          padding: const EdgeInsets.only(bottom: 16),
          children: [
            // Search bar and warehouse type switch
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Search bar
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Update the current tab to search (index 2) and let the navigation happen through the tab bar
                        ref.read(currentTabProvider.notifier).state = 2;
                        context.go('/search');
                      },
                      child: Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.search,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Search your need...',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Warehouse type switch
                  const WarehouseSwitch(),
                ],
              ),
            ),

            // Categories content
            categoriesAsync.when(
              data: (categories) {
                // Filter only COLLECTION type categories
                final collections = categories
                    .where((category) =>
                        category.type == CategoryType.COLLECTION &&
                        category.children?.isNotEmpty == true)
                    .toList();

                if (collections.isEmpty) {
                  return const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text('No categories available'),
                    ),
                  );
                }

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: collections.map((collection) {
                      return _buildCollectionSection(context, collection);
                    }).toList(),
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('Error: ${error.toString()}'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollectionSection(BuildContext context, Category collection) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Collection title
        Text(
          collection.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          textAlign: TextAlign.start,
        ),

        const SizedBox(height: 16),

        // Categories grid
        if (collection.children != null && collection.children!.isNotEmpty)
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 16,
              childAspectRatio: 0.7,
            ),
            itemCount: collection.children!.length,
            itemBuilder: (context, index) {
              final category = collection.children![index];
              return _buildCategoryCard(context, category);
            },
          ),
        if (collection.children!.isEmpty)
          Center(
            child: Text('No categories under this section.'),
          )
      ],
    );
  }

  Widget _buildCategoryCard(BuildContext context, Category category) {
    return GestureDetector(
      onTap: () {
        context.pushNamed(
          'category-details',
          pathParameters: {'categoryId': category.id.toString()},
        );
      },
      child: Column(
        children: [
          // Category image container
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: category.iconUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      category.iconUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.category,
                            color: Colors.grey.shade400,
                            size: 30,
                          ),
                        );
                      },
                    ),
                  )
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.category,
                      color: Colors.grey.shade400,
                      size: 30,
                    ),
                  ),
          ),

          const SizedBox(height: 8),

          // Category name
          Text(
            category.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
