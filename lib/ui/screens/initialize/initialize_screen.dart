import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/util.dart';

class InitializeScreen extends StatefulWidget {
  const InitializeScreen({super.key});

  @override
  State<InitializeScreen> createState() => _InitializeScreenState();
}

class _InitializeScreenState extends State<InitializeScreen> {
  late SharedPreferences sharedPreferences;
  Api api = Api();

  Future<User?> getUser() async {
    try {
      await Future.delayed(Duration(seconds: 1));
      sharedPreferences = await SharedPreferences.getInstance();

      var token = sharedPreferences.getString("token");

      if (token == null) {
        return null;
      }

      var data = await api.get("users");
      if (data == null) {
        return null;
      }

      var user = userFromJson(data);

      // Update Firebase token
      try {
        FirebaseMessaging messaging = FirebaseMessaging.instance;
        await messaging.requestPermission();
        final firebaseToken = await messaging.getToken();

        if (firebaseToken != null) {
          await Api().patch('users/firebase', data: {'token': firebaseToken});
          print('Firebase token updated successfully');
        }
      } catch (e) {
        print('Error updating Firebase token: $e');
        // Continue anyway, this is not critical
      }

      return user;
    } on Exception {
      print('here????');
      return null;
    }
  }

  @override
  void initState() {
    super.initState();
    _checkUserAndNavigate();
  }

  Future<void> _checkUserAndNavigate() async {
    final navigator = GoRouter.of(context);

    try {
      final user = await getUser();

      if (user == null) {
        navigator.pushReplacementNamed("login");
      } else {
        if (user.type == 'WAREHOUSE_STAFF') {
          navigator.go("/home");
        } else {
          await sharedPreferences.clear();
          Util.showErrorToast(
            "You are unauthorized. Only warehouse staffs are authorized.",
          );
          navigator.pushReplacementNamed("login");
        }
      }
    } catch (e, stacktrace) {
      print("Error while checking user: $e");
      print(stacktrace);
      Util.showErrorToast("Something went wrong.");
      navigator.pushReplacementNamed("login");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Center(
        child: CircularProgressIndicator(
          color: Colors.black,
        ),
      ),
    );
  }
}
