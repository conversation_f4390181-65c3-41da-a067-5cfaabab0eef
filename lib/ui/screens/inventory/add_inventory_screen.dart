import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_warehouse/domain/model/warehouse.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/network/dto/create_inventory_batch_dto.dart';
import 'package:vegmove_warehouse/network/dto/staff_details_response.dart';
import 'package:vegmove_warehouse/ui/components/create_inventory_item_card.dart';
import 'package:vegmove_warehouse/util.dart';

import '../../../domain/model/supplier.dart';

class AddInventoryPage extends StatefulWidget {
  const AddInventoryPage({Key? key}) : super(key: key);

  @override
  State<AddInventoryPage> createState() => _AddInventoryPageState();
}

class _AddInventoryPageState extends State<AddInventoryPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _batchNameController = TextEditingController();
  final Api _apiService = Api();
  bool _isSubmitting = false;
  List<CreateInventoryItemDto> _inventories = [];

  List<Warehouse> _warehouses = [];
  List<Supplier> _suppliers = [];
  Warehouse? _selectedWarehouse;

  @override
  void dispose() {
    _batchNameController.dispose();
    super.dispose();
  }

  Future<void> fetchStaffDetails() async {
    try {
      String? data = await _apiService.get("staffs");

      StaffDetailsResponse staffDetailsResponse =
          staffDetailsResponseFromJson(data!);

      setState(() {
        _warehouses = staffDetailsResponse.warehouses;
        if (_warehouses.isNotEmpty) {
          _selectedWarehouse = _warehouses[0];
        }
      });
    } on Exception catch (e) {
      Util.showErrorToast(e.toString());
    }
  }

  Future<void> fetchSuppliers() async {
    try {
      final response = await Api().get('admin/suppliers');
      final suppliers = supplierFromJson(response!);

      setState(() {
        _suppliers = suppliers;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load suppliers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    fetchStaffDetails();
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_inventories.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add at least one inventory item'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      if (_selectedWarehouse == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a warehouse'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      setState(() {
        _isSubmitting = true;
      });

      try {
        final batch = CreateInventoryBatchDto(
          batchName: _batchNameController.text,
          warehouseId: _selectedWarehouse!.id,
          inventories: _inventories,
        );

        var response =
            await _apiService.post('admin/inventories', data: batch.toJson());

        if (response == null) {
          Util.showSomethingWentWrongToast();
          return;
        }

        Map<String, dynamic> responseData = jsonDecode(response);

        if (responseData["statusCode"] != null &&
            responseData["statusCode"] == 500) {
          Util.showErrorToast(responseData["message"]);
          return;
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Inventory items have been added successfully'),
              backgroundColor: Colors.green,
            ),
          );
          GoRouter.of(context).pop();
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${error.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }

  Future<void> _scanBarcode() async {
    try {
      String barcodeScanRes = await FlutterBarcodeScanner.scanBarcode(
        '#FF6666',
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcodeScanRes != '-1') {
        _showItemEditor(barcode: barcodeScanRes);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error scanning barcode: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showItemEditor(
      {CreateInventoryItemDto? item, String? barcode, int? index}) {
    showDialog(
      context: context,
      builder: (context) => InventoryItemDialog(
        suppliers: _suppliers,
        item: item,
        barcode: barcode,
        onSave: (updatedItem) {
          setState(() {
            if (item == null) {
              _inventories.add(updatedItem);
            } else if (index != null) {
              _inventories[index] = updatedItem;
            }
          });
        },
      ),
    );
  }

  void _removeInventoryItem(int index) {
    setState(() {
      _inventories.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: const Text('Add New Inventory'),
        actions: [
          TextButton.icon(
            onPressed: _isSubmitting ? null : _submitForm,
            icon: const Icon(Icons.save, color: Colors.white),
            label: Text(
              _isSubmitting ? 'Saving...' : 'Save Batch',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextFormField(
                    controller: _batchNameController,
                    decoration: const InputDecoration(
                      labelText: 'Batch Number',
                      border: OutlineInputBorder(),
                      hintText: 'Enter batch number',
                      prefixIcon: Icon(Icons.batch_prediction),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Batch name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<Warehouse>(
                    decoration: const InputDecoration(
                      labelText: 'Select Warehouse',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.warehouse),
                    ),
                    value: _selectedWarehouse,
                    items: _warehouses.map((wh) {
                      return DropdownMenuItem<Warehouse>(
                        value: wh,
                        child: Text(wh.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedWarehouse = value;
                      });
                    },
                    validator: (value) =>
                        value == null ? 'Please select a warehouse' : null,
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Items (${_inventories.length})',
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      _showItemEditor();
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Add Manually'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: _inventories.isEmpty
                  ? const Center(
                      child: Text(
                        'No items added yet.\nScan a barcode or add manually.',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(8.0),
                      itemCount: _inventories.length,
                      itemBuilder: (context, index) {
                        final item = _inventories[index];
                        return CreateInventoryItemCard(
                          item: item,
                          onTap: () {
                            _showItemEditor(item: item, index: index);
                          },
                          onDelete: () {
                            _removeInventoryItem(index);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _scanBarcode,
        tooltip: 'Scan Barcode',
        child: const Icon(Icons.qr_code_scanner),
      ),
    );
  }
}

class InventoryItemDialog extends StatefulWidget {
  final CreateInventoryItemDto? item;
  final String? barcode;
  final Function(CreateInventoryItemDto) onSave;
  final List<Supplier> suppliers;

  const InventoryItemDialog({
    super.key,
    required this.item,
    required this.barcode,
    required this.onSave,
    required this.suppliers,
  });

  @override
  State<InventoryItemDialog> createState() => _InventoryItemDialogState();
}

class _InventoryItemDialogState extends State<InventoryItemDialog> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _barcodeController = TextEditingController();
  final TextEditingController _initialStockController = TextEditingController();
  final TextEditingController _buyingPriceController = TextEditingController();
  final TextEditingController _sellingPriceController = TextEditingController();

  DateTime? manufactureDate;
  DateTime? expiryDate;

  Supplier? _selectedSupplier;

  @override
  void initState() {
    super.initState();
    _barcodeController.text =
        widget.item?.barcode.toString() ?? widget.barcode?.toString() ?? '';
    _initialStockController.text = widget.item?.initialStock.toString() ?? '';
    _buyingPriceController.text = widget.item?.buyingPrice.toString() ?? '';
    _sellingPriceController.text = widget.item?.sellingPrice.toString() ?? '';
  }

  @override
  void dispose() {
    _barcodeController.dispose();
    _initialStockController.dispose();
    _buyingPriceController.dispose();
    _sellingPriceController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isManufactureDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isManufactureDate
          ? manufactureDate ?? DateTime.now()
          : expiryDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      setState(() {
        if (isManufactureDate) {
          manufactureDate = picked;
        } else {
          expiryDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd MMM, yyyy');

    return AlertDialog(
      title: Text(widget.item != null
          ? 'Edit Item #${widget.item?.barcode}'
          : 'Add New Item'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _barcodeController,
                decoration: const InputDecoration(
                  labelText: 'Barcode',
                  prefixIcon: Icon(Icons.barcode_reader),
                ),
                // keyboardType: TextInputType.,
                // inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Barcode is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _initialStockController,
                decoration: const InputDecoration(
                  labelText: 'Initial Stock',
                  prefixIcon: Icon(Icons.inventory_2),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Initial stock is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _buyingPriceController,
                decoration: const InputDecoration(
                  labelText: 'Buying Price',
                  prefixIcon: Icon(Icons.account_balance_wallet),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*\$')),
                // ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Buying price is required';
                  }
                  final buyingPrice = double.tryParse(value);
                  if (buyingPrice == null || buyingPrice <= 0) {
                    return 'Please enter a valid buying price';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _sellingPriceController,
                decoration: const InputDecoration(
                  labelText: 'Selling Price',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                // inputFormatters: [
                //   FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*\$')),
                // ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Selling price is required';
                  }
                  final sellingPrice = double.tryParse(value);
                  if (sellingPrice == null || sellingPrice <= 0) {
                    return 'Please enter a valid selling price';
                  }

                  // Check if buying price is less than selling price
                  final buyingPriceText = _buyingPriceController.text;
                  if (buyingPriceText.isNotEmpty) {
                    final buyingPrice = double.tryParse(buyingPriceText);
                    if (buyingPrice != null && buyingPrice >= sellingPrice) {
                      return 'Selling price must be greater than buying price';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<Supplier>(
                decoration: const InputDecoration(
                  labelText: 'Select Supplier (optional)',
                  prefixIcon: Icon(Icons.business),
                  border: OutlineInputBorder(),
                ),
                value: _selectedSupplier,
                items: widget.suppliers.map((supplier) {
                  return DropdownMenuItem<Supplier>(
                    value: supplier,
                    child: Text(supplier.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedSupplier = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.calendar_today),
                title: const Text('Manufacture Date (Optional)'),
                subtitle: Text(
                  manufactureDate != null
                      ? dateFormat.format(manufactureDate!)
                      : 'Not set',
                ),
                onTap: () => _selectDate(context, true),
              ),
              ListTile(
                leading: const Icon(Icons.event_busy),
                title: const Text('Expiry Date (Required)'),
                subtitle: Text(
                  expiryDate != null
                      ? dateFormat.format(expiryDate!)
                      : 'Not set',
                  style: TextStyle(
                    color: expiryDate == null ? Colors.red : null,
                  ),
                ),
                onTap: () => _selectDate(context, false),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              // Additional validation for expiry date
              if (expiryDate == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Expiry date is required'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              // Additional validation for price comparison
              final buyingPrice = double.parse(_buyingPriceController.text);
              final sellingPrice = double.parse(_sellingPriceController.text);
              if (buyingPrice >= sellingPrice) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Buying price must be less than selling price'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              widget.onSave(
                CreateInventoryItemDto(
                  barcode: _barcodeController.text,
                  initialStock: int.parse(_initialStockController.text),
                  buyingPrice: buyingPrice,
                  sellingPrice: sellingPrice,
                  supplierId: _selectedSupplier?.id,
                  manufactureDate: manufactureDate,
                  expiryDate: expiryDate!,
                ),
              );
              Navigator.of(context).pop();
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
