import 'package:vegmove_delivery/domain/model/delivery_driver_profile.dart';
import 'package:vegmove_delivery/services/driver_service.dart';
import 'package:vegmove_delivery/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class SettingsScreenViewModel extends ViewModel {
  late MutableState<bool> isLoading;
  late MutableState<bool> isUpdatingStatus;
  late MutableState<DeliveryDriverProfile?> driverProfile;

  final DriverService _driverService = DriverService();

  @override
  void init() {
    isLoading = mutableStateOf(true);
    isUpdatingStatus = mutableStateOf(false);
    driverProfile = mutableStateOf(null);
    
    _loadDriverProfile();
  }

  Future<void> _loadDriverProfile() async {
    try {
      isLoading(true);
      final profile = await _driverService.getDriverProfile();
      
      if (profile != null) {
        driverProfile(profile);
      } else {
        Util.showErrorToast('Failed to load profile');
      }
    } catch (e) {
      print('Error loading driver profile: $e');
      Util.showErrorToast('Failed to load profile');
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateDriverStatus(bool isActive) async {
    try {
      isUpdatingStatus(true);
      final updatedProfile = await _driverService.updateDriverStatus(isActive: isActive);
      
      if (updatedProfile != null) {
        driverProfile(updatedProfile);
        Util.showSuccessToast(
          isActive 
            ? 'You are now available for deliveries' 
            : 'You are now unavailable for deliveries'
        );
      } else {
        Util.showErrorToast('Failed to update status');
      }
    } catch (e) {
      print('Error updating driver status: $e');
      Util.showErrorToast('Failed to update status');
    } finally {
      isUpdatingStatus(false);
    }
  }

  void refreshProfile() {
    _loadDriverProfile();
  }
}
