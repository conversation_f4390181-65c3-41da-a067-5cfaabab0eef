import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_delivery/providers/location_tracker_provider.dart';
import 'package:vegmove_delivery/services/background_location_service.dart';
import 'package:vegmove_delivery/ui/screens/settings/settings_screen_viewmodel.dart';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  // Handle logout with proper context handling
  Future<void> _handleLogout(BuildContext context, WidgetRef ref) async {
    // Get router before async operations
    final router = GoRouter.of(context);

    // Stop location tracking
    ref.read(locationTrackerProvider.notifier).stopTracking();
    await BackgroundLocationService.stopService();

    // Remove token
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');

    // Navigate to login screen
    router.pushReplacementNamed('login');
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ViewModelBuilder<SettingsScreenViewModel>(
      viewModelBuilder: () => SettingsScreenViewModel(),
      builder: (context, viewModel) {
        final profile = viewModel.driverProfile.state;
        final isLoading = viewModel.isLoading.state;
        final isUpdatingStatus = viewModel.isUpdatingStatus.state;

        return Scaffold(
          appBar: AppBar(
            title: const Text('Settings'),
            backgroundColor: Colors.deepPurple,
            foregroundColor: Colors.white,
          ),
          body: isLoading
              ? const Center(child: CircularProgressIndicator())
              : profile == null
                  ? const Center(child: Text('Failed to load profile'))
                  : ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Profile Section
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Profile Information',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 30,
                                      backgroundColor: Colors.deepPurple,
                                      child: Text(
                                        profile.user.name.isNotEmpty
                                            ? profile.user.name[0].toUpperCase()
                                            : 'D',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            profile.user.name,
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            profile.user.email,
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                          if (profile.user.phone != null) ...[
                                            const SizedBox(height: 4),
                                            Text(
                                              profile.user.phone!,
                                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Active Status Section
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Availability Status',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Toggle your availability to receive new delivery assignments',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  children: [
                                    Icon(
                                      profile.isActive ? Icons.check_circle : Icons.cancel,
                                      color: profile.isActive ? Colors.green : Colors.red,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        profile.isActive ? 'Available for deliveries' : 'Not available for deliveries',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: profile.isActive ? Colors.green : Colors.red,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    if (isUpdatingStatus)
                                      const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      )
                                    else
                                      Switch(
                                        value: profile.isActive,
                                        onChanged: (value) {
                                          viewModel.updateDriverStatus(value);
                                        },
                                        activeColor: Colors.green,
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Logout Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () => _handleLogout(context, ref),
                            icon: const Icon(Icons.logout),
                            label: const Text('Logout'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
        );
      },
    );
  }
}
