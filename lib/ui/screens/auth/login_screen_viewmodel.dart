import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class LoginScreenViewModel extends ViewModel {
  late MutableState<bool> isLoading;
  late SharedPreferences sharedPreferences;
  Api api = Api();

  @override
  void init() {
    isLoading = mutableStateOf(false);
  }

  void login(String email, String password, VoidCallback onLogin) async {
    isLoading(true);
    try {
      sharedPreferences = await SharedPreferences.getInstance();

      var body = {
        "email": email,
        "password": password,
      };

      var response = await api.post("auth/login", data: body);

      if (response == null) {
        isLoading(false);
        Util.showSomethingWentWrongToast();
        return;
      }

      Map<String, dynamic> responseData = jsonDecode(response);

      String? token = responseData["token"];

      if (token != null) {
        await sharedPreferences.setString("token", token);
        onLogin();
      } else {
        Util.showErrorToast(responseData["message"]);
      }

      isLoading(false);
    } on Exception catch (e) {
      isLoading(false);
      print(e.toString());
    }
  }
}
