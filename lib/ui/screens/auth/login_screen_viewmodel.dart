import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_delivery/network/api.dart';
import 'package:vegmove_delivery/providers/location_tracker_provider.dart';
import 'package:vegmove_delivery/services/background_location_service.dart';
import 'package:vegmove_delivery/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class LoginScreenViewModel extends ViewModel {
  late MutableState<bool> isLoading;
  late SharedPreferences sharedPreferences;
  Api api = Api();

  @override
  void init() {
    isLoading = mutableStateOf(false);
  }

  void login(String email, String password, VoidCallback onLogin) async {
    isLoading(true);
    try {
      sharedPreferences = await SharedPreferences.getInstance();

      var body = {
        'email': email,
        'password': password,
      };

      var response = await api.post('auth/login', data: body);

      if (response == null) {
        isLoading(false);
        Util.showSomethingWentWrongToast();
        return;
      }

      Map<String, dynamic> responseData = jsonDecode(response);

      String? token = responseData['token'];

      if (token != null) {
        await sharedPreferences.setString('token', token);

        // Start location tracking after successful login
        _startLocationTracking();

        onLogin();
      } else {
        Util.showErrorToast(responseData['message']);
      }

      isLoading(false);
    } on Exception catch (e) {
      isLoading(false);
      print(e.toString());
    }
  }

  // Start location tracking
  void _startLocationTracking() async {
    try {
      // Get the container to access providers
      final container = ProviderContainer();

      // Start foreground location tracking
      container.read(locationTrackerProvider.notifier).startTracking();

      // Start background location service
      await BackgroundLocationService.startService();

      print('Location tracking started');
    } catch (e) {
      print('Error starting location tracking: $e');
    }
  }
}
