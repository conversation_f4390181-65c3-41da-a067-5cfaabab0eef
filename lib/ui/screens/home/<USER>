import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_warehouse/ui/components/inventory_batch_card.dart';
import 'package:vegmove_warehouse/ui/screens/home/<USER>';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<HomeScreenViewModel>(
      viewModelBuilder: () {
        return HomeScreenViewModel();
      },
      builder: (context, viewModel) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Inventory batches'),
            actions: [
              IconButton(
                onPressed: () async {
                  GoRouter router = GoRouter.of(context);
                  SharedPreferences sharedPreferences =
                      await SharedPreferences.getInstance();

                  await sharedPreferences.remove("token");
                  router.pushReplacementNamed("login");
                },
                icon: Icon(
                  Icons.logout,
                  // color: Colors.red,
                ),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () async {
              await GoRouter.of(context).pushNamed('add-inventory');
              await viewModel.getInventoryBatches();
            },
            tooltip: 'Add new batch',
            child: const Icon(Icons.inventory),
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await viewModel.getInventoryBatches();
            },
            child: ListView.separated(
              itemBuilder: (context, index) {
                final inventoryBatch = viewModel.inventoryBatches[index];

                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: InventoryBatchCard(batch: inventoryBatch),
                );
              },
              separatorBuilder: (context, index) {
                return Divider(
                  height: 0.5,
                );
              },
              itemCount: viewModel.inventoryBatches.length,
            ),
          ),
        );
      },
    );
  }
}
