import 'package:vegmove_warehouse/domain/model/inventory_batch.dart';
import 'package:vegmove_warehouse/domain/model/pagination_data.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/network/dto/inventory_batch_response.dart';
import 'package:vegmove_warehouse/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class HomeScreenViewModel extends ViewModel {
  late MutableStateList<InventoryBatch> inventoryBatches;
  late MutableState<PaginationData> paginationData;
  late MutableState<bool> isLoading;

  Api api = Api();

  @override
  void init() {
    inventoryBatches = mutableStateListOf();
    paginationData = mutableStateOf(PaginationData(
      total: 0,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    ));

    isLoading = mutableStateOf(true);

    getInventoryBatches(
      page: paginationData.state.page,
      pageSize: paginationData.state.pageSize,
    );
  }

  Future<void> getInventoryBatches({
    int? page,
    int? pageSize,
  }) async {
    try {
      var data = await api.get(
        "admin/inventories/batches",
        queries: {
          'page': page ?? paginationData.state.page,
          'pageSize': pageSize ?? paginationData.state.pageSize,
        },
      );
      if (data == null) {
        Util.showSomethingWentWrongToast();
      }

      var inventoryBatchResponse = inventoryBatchResponseFromJson(data!);

      inventoryBatches.replaceAll(inventoryBatchResponse.data);
      paginationData(
        PaginationData(
          total: inventoryBatchResponse.total,
          page: inventoryBatchResponse.page,
          pageSize: inventoryBatchResponse.pageSize,
          totalPages: inventoryBatchResponse.totalPages,
        ),
      );
    } on Exception catch (e) {
      Util.showErrorToast(e.toString());
    }
  }
}
