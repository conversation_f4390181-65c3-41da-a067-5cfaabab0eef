import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_delivery/domain/model/order.dart';
import 'package:vegmove_delivery/domain/model/pagination_data.dart';
import 'package:vegmove_delivery/network/api.dart';
import 'package:vegmove_delivery/network/dto/orders_response.dart';
import 'package:vegmove_delivery/providers/location_tracker_provider.dart';
import 'package:vegmove_delivery/services/background_location_service.dart';
import 'package:vegmove_delivery/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class HomeScreenViewModel extends ViewModel {
  late MutableStateList<Order> orders;
  late MutableState<PaginationData> paginationData;
  late MutableState<bool> isLoading;
  late MutableState<bool> isLoadingMore;

  final Api api = Api();

  @override
  void init() {
    orders = mutableStateListOf();
    paginationData = mutableStateOf(PaginationData(
      total: 0,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    ));
    isLoading = mutableStateOf(true);
    isLoadingMore = mutableStateOf(false);

    // Load the first page.
    fetchOrders(page: 1).then((_) {
      // Check for active shipping orders
      _checkForActiveShippingOrders();
    });
  }

  // Check if there are any active shipping orders and update location tracking accordingly
  void _checkForActiveShippingOrders() {
    try {
      final hasActiveShippingOrder =
          orders.any((order) => order.status == 'SHIPPED');

      if (hasActiveShippingOrder) {
        print(
            'Active shipping order found, updating location tracking frequency');

        // Get the container to access providers
        final container = ProviderContainer();

        // Update foreground location tracking
        container
            .read(locationTrackerProvider.notifier)
            .updateShippingOrderStatus(true);

        // Update background service if running
        BackgroundLocationService.updateShippingOrderStatus(true);
      }
    } catch (e) {
      print('Error checking for active shipping orders: $e');
    }
  }

  Future<void> fetchOrders({int? page, int? pageSize}) async {
    // Determine the current page.
    final int currentPage = page ?? paginationData.state.page;

    // Set appropriate loading flag.
    if (currentPage > 1) {
      isLoadingMore(true);
    } else {
      isLoading(true);
    }
    try {
      final response = await api.get(
        'delivery-drivers/orders',
        queries: {
          'page': currentPage,
          'pageSize': pageSize ?? paginationData.state.pageSize,
        },
      );

      if (response == null) {
        Util.showSomethingWentWrongToast();
        return;
      }

      final ordersResponse = ordersResponseFromJson(response);

      // Append orders if loading subsequent pages, else replace.
      if (currentPage > 1) {
        orders.addAll(ordersResponse.data);
      } else {
        orders.replaceAll(ordersResponse.data);
      }

      // Update pagination data.
      paginationData(
        PaginationData(
          total: ordersResponse.total,
          page: ordersResponse.page,
          pageSize: ordersResponse.pageSize,
          totalPages: ordersResponse.totalPages,
        ),
      );

      // If this is the first page and we're refreshing the orders,
      // check for active shipping orders
      if (currentPage == 1) {
        _checkForActiveShippingOrders();
      }
    } catch (e) {
      Util.showErrorToast(e.toString());
    } finally {
      if (currentPage > 1) {
        isLoadingMore(false);
      } else {
        isLoading(false);
      }
    }
  }
}
