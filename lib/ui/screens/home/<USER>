import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_delivery/ui/screens/home/<USER>';
import 'package:viewmodel_flutter/viewmodel_builder.dart';
import 'package:intl/intl.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  String _formatDate(DateTime date) {
    return DateFormat.yMMMd().format(date);
  }

  Widget _buildOrderCard(BuildContext context, dynamic order) {
    final customer = order.user;
    final address = order.address;

    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          GoRouter.of(context).pushNamed(
            'orderDetails',
            pathParameters: {'id': order.id.toString()},
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order title and navigation icon.
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Order #${order.id}',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const Icon(Icons.arrow_forward_ios, size: 16),
                ],
              ),
              const SizedBox(height: 8),
              // Customer information.
              Row(
                children: [
                  const Icon(Icons.person, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      customer.name,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Order status.
              Row(
                children: [
                  const Icon(Icons.info_outline, size: 18),
                  const SizedBox(width: 6),
                  Text(
                    'Status: ${order.status}',
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Delivery date and time.
              Row(
                children: [
                  const Icon(Icons.schedule, size: 18),
                  const SizedBox(width: 6),
                  Text(
                    'Delivery: ${_formatDate(order.deliveryDate)} between ${order.deliveryStartTime} and ${order.deliveryEndTime}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Delivery address.
              Row(
                children: [
                  const Icon(Icons.location_on, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '${address.streetName}, ${address.city}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ViewModelBuilder<HomeScreenViewModel>(
      viewModelBuilder: () => HomeScreenViewModel(),
      builder: (context, viewModel) {
        final pagination = viewModel.paginationData.state;
        return Scaffold(
          appBar: AppBar(
            title: const Text('Assigned Orders'),
          ),
          body: RefreshIndicator(
            onRefresh: () => viewModel.fetchOrders(page: 1),
            child: viewModel.isLoading.state && viewModel.orders.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : viewModel.orders.isEmpty
                    ? ListView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height - 100,
                            width: double.infinity,
                            child: const Center(
                              child: Text('No assigned orders'),
                            ),
                          ),
                        ],
                      )
                    : NotificationListener<ScrollNotification>(
                        onNotification: (scrollInfo) {
                          // Check if scrolled to the bottom and more pages remain.
                          if (!viewModel.isLoadingMore.state &&
                              scrollInfo.metrics.pixels ==
                                  scrollInfo.metrics.maxScrollExtent &&
                              pagination.page < pagination.totalPages) {
                            viewModel.fetchOrders(page: pagination.page + 1);
                          }
                          return false;
                        },
                        child: ListView.builder(
                          padding: const EdgeInsets.only(top: 16, bottom: 16),
                          itemCount: viewModel.orders.length +
                              (pagination.page < pagination.totalPages ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index < viewModel.orders.length) {
                              final order = viewModel.orders[index];
                              return _buildOrderCard(context, order);
                            } else {
                              // Loader indicator for pagination at the bottom.
                              return Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Center(
                                  child: viewModel.isLoadingMore.state
                                      ? const CircularProgressIndicator()
                                      : const SizedBox.shrink(),
                                ),
                              );
                            }
                          },
                        ),
                      ),
          ),
        );
      },
    );
  }
}
