import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Provider for the current tab index
final currentTabProvider = StateProvider<int>((ref) => 0);

class NavigationScreen extends ConsumerWidget {
  final Widget child;

  const NavigationScreen({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTab = ref.watch(currentTabProvider);

    return Scaffold(
      body: child,
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            currentIndex: currentTab,
            onTap: (index) {
              ref.read(currentTabProvider.notifier).state = index;

              // Navigate to the appropriate tab
              switch (index) {
                case 0:
                  context.go('/home');
                  break;
                case 1:
                  context.go('/categories');
                  break;
                case 2:
                  context.go('/search');
                  break;
                case 3:
                  context.go('/my-bag');
                  break;
                case 4:
                  context.go('/settings');
                  break;
              }
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.white,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.darkGrey,
            selectedLabelStyle: const TextStyle(
              fontFamily: 'RedHatDisplay',
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: 'RedHatDisplay',
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
            items: [
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/images/ic_home.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.darkGrey,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/images/ic_home.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/images/ic_categories.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.darkGrey,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/images/ic_categories.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                label: 'Categories',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/images/ic_search.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.darkGrey,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/images/ic_search.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                label: 'Search',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/images/ic_bag.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.darkGrey,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/images/ic_bag.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                label: 'My Bag',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  'assets/images/ic_settings.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.darkGrey,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                activeIcon: SvgPicture.asset(
                  'assets/images/ic_settings.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                  height: 24,
                ),
                label: 'Settings',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
