import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/network/dto/delivery_drivers_response.dart';
import 'package:vegmove_warehouse/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class DeliveryDriverSelectionViewModel extends ViewModel {
  final int? warehouseId;

  late MutableState<bool> isLoading;
  late MutableState<bool> isLoadingMore;
  late MutableStateList<User> drivers;
  late TextEditingController searchController;
  late ScrollController scrollController;

  int currentPage = 1;
  int totalPages = 1;
  int pageSize = 10;
  String searchQuery = '';
  Timer? _debounce;

  Api api = Api();

  DeliveryDriverSelectionViewModel({this.warehouseId});

  @override
  void init() {
    isLoading = mutableStateOf(true);
    isLoadingMore = mutableStateOf(false);
    drivers = mutableStateListOf();
    searchController = TextEditingController();
    scrollController = ScrollController();

    scrollController.addListener(_scrollListener);

    loadDeliveryDrivers();
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (currentPage < totalPages && !isLoadingMore.state) {
        loadMoreDrivers();
      }
    }
  }

  Future<void> loadDeliveryDrivers() async {
    isLoading(true);
    currentPage = 1;

    try {
      final response = await api.get(
        'admin/delivery-drivers',
        queries: {
          'page': currentPage,
          'pageSize': pageSize,
          if (searchQuery.isNotEmpty) 'search': searchQuery,
          if (warehouseId != null) 'warehouseId': warehouseId,
        },
      );

      if (response == null) {
        Util.showErrorToast('Failed to load delivery drivers');
        isLoading(false);
        return;
      }

      final DeliveryDriversResponse driversResponse =
          DeliveryDriversResponse.fromJson(json.decode(response));

      drivers.replaceAll(driversResponse.data);
      totalPages = driversResponse.totalPages;
    } catch (e, stacktrace) {
      print('Error loading delivery drivers: $e');
      debugPrint(stacktrace.toString());
      Util.showErrorToast('Failed to load delivery drivers');
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadMoreDrivers() async {
    if (currentPage >= totalPages) return;

    isLoadingMore(true);
    currentPage++;

    try {
      final response = await api.get(
        'admin/delivery-drivers',
        queries: {
          'page': currentPage.toString(),
          'pageSize': pageSize.toString(),
          if (searchQuery.isNotEmpty) 'search': searchQuery,
          if (warehouseId != null) 'warehouseId': warehouseId,
        },
      );

      if (response == null) {
        Util.showErrorToast('Failed to load more delivery drivers');
        isLoadingMore(false);
        currentPage--;
        return;
      }

      final DeliveryDriversResponse driversResponse =
          DeliveryDriversResponse.fromJson(json.decode(response));

      drivers.addAll(driversResponse.data);
    } catch (e) {
      print('Error loading more delivery drivers: $e');
      Util.showErrorToast('Failed to load more delivery drivers');
      currentPage--;
    } finally {
      isLoadingMore(false);
    }
  }

  void onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (searchQuery != query) {
        searchQuery = query;
        loadDeliveryDrivers();
      }
    });
  }

  void clearSearch() {
    searchController.clear();
    searchQuery = '';
    loadDeliveryDrivers();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    searchController.dispose();
    scrollController.dispose();
    super.dispose();
  }
}
