import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_warehouse/domain/model/enums.dart';
import 'package:vegmove_warehouse/domain/model/order.dart';
import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/network/dto/update_order_status_dto.dart';
import 'package:vegmove_warehouse/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class OrderDetailsScreenViewModel extends ViewModel {
  final int orderId;
  late MutableState<bool> isLoading;
  late MutableState<Order?> order;
  late MutableStateList<User> deliveryDrivers;

  Api api = Api();

  OrderDetailsScreenViewModel(this.orderId);

  @override
  void init() {
    isLoading = mutableStateOf(false);
    order = mutableStateOf(null);
    deliveryDrivers = mutableStateListOf();

    loadOrderDetails();
  }

  Future<void> loadOrderDetails() async {
    isLoading(true);
    try {
      final response = await api.get('staffs/orders/$orderId');
      if (response == null) {
        Util.showErrorToast('Failed to load order details');
        isLoading(false);
        return;
      }

      order(Order.fromJson(json.decode(response)));
    } catch (e, stacktrace) {
      print('Error loading order details: $e');
      debugPrint(stacktrace.toString());
      Util.showErrorToast('Failed to load order details');
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateOrderStatus(OrderStatus status) async {
    if (order.state == null) return;

    isLoading(true);
    try {
      final dto = UpdateOrderStatusDto(orderStatus: status);
      final response =
          await api.patch('staffs/orders/$orderId', data: dto.toJson());
      if (response == null) {
        Util.showErrorToast('Failed to update order status');
        isLoading(false);
        return;
      }

      order(Order.fromJson(json.decode(response)));
      Util.showSuccessToast('Order status updated successfully');
    } catch (e) {
      print('Error updating order status: $e');
      Util.showErrorToast('Failed to update order status');
    } finally {
      isLoading(false);
    }
  }

  Future<void> updatePaymentStatus(PaymentStatus status) async {
    if (order.state == null) return;

    isLoading(true);
    try {
      final dto = UpdateOrderStatusDto(paymentStatus: status.name);
      final response =
          await api.patch('staffs/orders/$orderId', data: dto.toJson());
      if (response == null) {
        Util.showErrorToast('Failed to update payment status');
        isLoading(false);
        return;
      }

      order(Order.fromJson(json.decode(response)));
      Util.showSuccessToast('Payment status updated successfully');
    } catch (e) {
      print('Error updating payment status: $e');
      Util.showErrorToast('Failed to update payment status');
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadDeliveryDrivers() async {
    try {
      final response = await api.get('admin/delivery-drivers');
      if (response == null) {
        Util.showErrorToast('Failed to load delivery drivers');
        return;
      }

      final data = json.decode(response);
      if (data['data'] != null) {
        deliveryDrivers.replaceAll(
            List<User>.from(data['data'].map((x) => User.fromJson(x))));
      }
    } catch (e) {
      print('Error loading delivery drivers: $e');
      Util.showErrorToast('Failed to load delivery drivers');
    }
  }

  Future<void> assignDriver(int driverId) async {
    if (order.state == null) return;

    isLoading(true);
    try {
      final dto = UpdateOrderStatusDto(deliveryDriverId: driverId);
      final response =
          await api.patch('staffs/orders/$orderId', data: dto.toJson());
      if (response == null) {
        Util.showErrorToast('Failed to assign driver');
        isLoading(false);
        return;
      }

      order(Order.fromJson(json.decode(response)));
      Util.showSuccessToast('Driver assigned successfully');
    } catch (e, stacktrace) {
      print('Error assigning driver: $e');
      print(stacktrace.toString());
      Util.showErrorToast('Failed to assign driver');
    } finally {
      isLoading(false);
    }
  }

  Future<void> showAssignDriverDialog(BuildContext context) async {
    final warehouseId = order.state?.warehouseId;

    final User? selectedDriver = await GoRouter.of(context).push<User>(
      '/delivery-driver-selection',
      extra: {'warehouseId': warehouseId},
    );

    if (selectedDriver != null) {
      assignDriver(selectedDriver.id);
    }
  }
}
