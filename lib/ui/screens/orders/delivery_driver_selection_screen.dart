import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/ui/screens/orders/delivery_driver_selection_viewmodel.dart';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class DeliveryDriverSelectionScreen extends StatelessWidget {
  final int? warehouseId;

  const DeliveryDriverSelectionScreen({super.key, this.warehouseId});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<DeliveryDriverSelectionViewModel>(
      viewModelBuilder: () =>
          DeliveryDriverSelectionViewModel(warehouseId: warehouseId),
      builder: (context, viewModel) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Select Delivery Driver'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                GoRouter.of(context).pop();
              },
            ),
          ),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text<PERSON><PERSON>(
                  controller: viewModel.searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by name, email, or phone',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: viewModel.searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              viewModel.clearSearch();
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    viewModel.onSearchChanged(value);
                  },
                ),
              ),
              Expanded(
                child: viewModel.isLoading.state
                    ? const Center(child: CircularProgressIndicator())
                    : viewModel.drivers.isEmpty
                        ? const Center(
                            child: Text('No delivery drivers found'),
                          )
                        : RefreshIndicator(
                            onRefresh: () => viewModel.loadDeliveryDrivers(),
                            child: ListView.builder(
                              controller: viewModel.scrollController,
                              itemCount: viewModel.isLoadingMore.state
                                  ? viewModel.drivers.length + 1
                                  : viewModel.drivers.length,
                              itemBuilder: (context, index) {
                                if (index >= viewModel.drivers.length) {
                                  return const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }
                                return _buildDriverItem(context,
                                    viewModel.drivers[index], viewModel);
                              },
                            ),
                          ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDriverItem(BuildContext context, User driver,
      DeliveryDriverSelectionViewModel viewModel) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: InkWell(
        onTap: () {
          GoRouter.of(context).pop(driver);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Stack(
                    children: [
                      driver.profilePicture != null
                          ? CircleAvatar(
                              backgroundImage:
                                  NetworkImage(driver.profilePicture!),
                              radius: 30,
                            )
                          : const CircleAvatar(
                              radius: 30,
                              child: Icon(Icons.person),
                            ),
                      // Active status indicator
                      if (driver.deliveryDriverProfile != null)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: driver.deliveryDriverProfile!.isActive
                                  ? Colors.green
                                  : Colors.red,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: Icon(
                              driver.deliveryDriverProfile!.isActive
                                  ? Icons.check
                                  : Icons.close,
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                driver.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (driver.deliveryDriverProfile != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: driver.deliveryDriverProfile!.isActive
                                      ? Colors.green.withValues(alpha: 0.1)
                                      : Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color:
                                        driver.deliveryDriverProfile!.isActive
                                            ? Colors.green
                                            : Colors.red,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  driver.deliveryDriverProfile!.isActive
                                      ? 'Active'
                                      : 'Inactive',
                                  style: TextStyle(
                                    color:
                                        driver.deliveryDriverProfile!.isActive
                                            ? Colors.green
                                            : Colors.red,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(driver.email),
                      ],
                    ),
                  ),
                ],
              ),

              // Location and Distance Information
              if (driver.lastLocation != null ||
                  driver.distanceFromWarehouse != null) ...[
                const Divider(height: 24),
                const Text(
                  'Location & Distance',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                IntrinsicHeight(
                  child: Row(
                    children: [
                      if (driver.lastLocation != null) ...[
                        Expanded(
                          child: _buildLocationInfo(driver),
                        ),
                      ],
                      if (driver.distanceFromWarehouse != null) ...[
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildDistanceInfo(driver),
                        ),
                      ],
                    ],
                  ),
                ),
              ],

              if (driver.performance != null) ...[
                const Divider(height: 24),
                const Text(
                  'Performance',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildPerformanceItem(
                      'Total Orders',
                      '${driver.performance!.totalOrderCount}',
                      Icons.shopping_bag,
                    ),
                    _buildPerformanceItem(
                      'Avg Pickup',
                      '${driver.performance!.avgPickupMins} mins',
                      Icons.access_time,
                    ),
                    _buildPerformanceItem(
                      'Avg Delivery',
                      '${driver.performance!.avgDeliveryMins} mins',
                      Icons.delivery_dining,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildPerformanceItem(
                      'Late Deliveries',
                      '${driver.performance!.lateDeliveryCount}',
                      Icons.warning,
                      color: driver.performance!.lateDeliveryCount > 0
                          ? Colors.orange
                          : null,
                    ),
                    _buildPerformanceItem(
                      'Avg Late Time',
                      '${driver.performance!.avgLateMins} mins',
                      Icons.timer_off,
                      color: driver.performance!.avgLateMins > 0
                          ? Colors.orange
                          : null,
                    ),
                    const SizedBox(width: 80), // For balance
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationInfo(User driver) {
    if (driver.lastLocation == null) return const SizedBox.shrink();

    final location = driver.lastLocation!;
    final timeAgo = _getTimeAgo(location.updatedAt);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.location_on, color: Colors.blue, size: 16),
              const SizedBox(width: 4),
              const Text(
                'Last Location',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Updated $timeAgo',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: () => _openGoogleMaps(location.lat, location.long),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.map, color: Colors.white, size: 16),
                  SizedBox(width: 4),
                  Text(
                    'View on Map',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistanceInfo(User driver) {
    if (driver.distanceFromWarehouse == null) return const SizedBox.shrink();

    final distance = driver.distanceFromWarehouse!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.straighten, color: Colors.orange, size: 16),
              const SizedBox(width: 4),
              const Text(
                'Distance',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${distance.distance} ${distance.unit}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          Text(
            'from warehouse',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _openGoogleMaps(String lat, String lng) async {
    final url = 'https://www.google.com/maps?q=$lat,$lng';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  Widget _buildPerformanceItem(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
