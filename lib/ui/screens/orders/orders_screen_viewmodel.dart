import 'package:vegmove_warehouse/domain/model/order.dart';
import 'package:vegmove_warehouse/domain/model/pagination_data.dart';
import 'package:vegmove_warehouse/network/api.dart';
import 'package:vegmove_warehouse/network/dto/orders_response.dart';
import 'package:vegmove_warehouse/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class OrdersScreenViewModel extends ViewModel {
  late MutableStateList<Order> orders;
  late MutableState<PaginationData> paginationData;
  late MutableState<bool> isLoading;
  String? _currentStatusFilter;

  Api api = Api();

  @override
  void init() {
    orders = mutableStateListOf();
    paginationData = mutableStateOf(PaginationData(
      total: 0,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    ));

    isLoading = mutableStateOf(true);

    loadOrders();
  }

  Future<void> loadOrders({bool refresh = true}) async {
    if (refresh) {
      paginationData(
        PaginationData(
          total: 0,
          page: 1,
          pageSize: 10,
          totalPages: 1,
        ),
      );
      orders.clear();
    }

    isLoading(true);
    try {
      final queryParams = {
        'page': paginationData.state.page.toString(),
        'pageSize': paginationData.state.pageSize.toString(),
      };

      if (_currentStatusFilter != null && _currentStatusFilter != 'ALL') {
        queryParams['status'] = _currentStatusFilter!;
      }

      final response = await api.get('staffs/orders', queries: queryParams);
      if (response == null) {
        Util.showErrorToast('Failed to load orders');
        isLoading(false);
        return;
      }

      final ordersResponse = ordersResponseFromJson(response);
      orders.addAll(ordersResponse.data);
      paginationData(
        PaginationData(
          total: ordersResponse.total,
          page: ordersResponse.page,
          pageSize: ordersResponse.pageSize,
          totalPages: ordersResponse.totalPages,
        ),
      );
    } catch (e, stacktrace) {
      print(stacktrace.toString());
      print('Error loading orders: $e');
      Util.showErrorToast('Failed to load orders');
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadMoreOrders() async {
    if (paginationData.state.page >= paginationData.state.totalPages) return;

    paginationData(
      PaginationData(
        total: paginationData.state.total,
        page: paginationData.state.page + 1,
        pageSize: paginationData.state.pageSize,
        totalPages: paginationData.state.totalPages,
      ),
    );
    await loadOrders(refresh: false);
  }

  void filterByStatus(String status) {
    _currentStatusFilter = status == 'ALL' ? null : status;
    loadOrders();
  }
}
