import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_warehouse/domain/model/enums.dart';
import 'package:vegmove_warehouse/domain/model/order.dart';
import 'package:vegmove_warehouse/domain/model/order_item.dart';
import 'package:vegmove_warehouse/ui/components/location_map.dart';
import 'package:vegmove_warehouse/ui/components/order_status_badge.dart';
import 'package:vegmove_warehouse/ui/components/payment_status_badge.dart';
import 'package:vegmove_warehouse/ui/components/route_map.dart';
import 'package:vegmove_warehouse/ui/screens/orders/order_details_screen_viewmodel.dart';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class OrderDetailsScreen extends StatelessWidget {
  final int orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<OrderDetailsScreenViewModel>(
      viewModelBuilder: () => OrderDetailsScreenViewModel(orderId),
      builder: (context, viewModel) {
        return Scaffold(
          appBar: AppBar(
            title: Text('Order #$orderId'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                GoRouter.of(context).go('/orders');
              },
            ),
            actions: [
              if (viewModel.order.state != null)
                PopupMenuButton<OrderStatus>(
                  onSelected: (value) {
                    viewModel.updateOrderStatus(value);
                  },
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.PENDING,
                      child: Text('Mark as Pending'),
                    ),
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.CONFIRMED,
                      child: Text('Mark as Confirmed'),
                    ),
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.READY,
                      child: Text('Mark as Ready'),
                    ),
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.SHIPPED,
                      child: Text('Mark as Shipped'),
                    ),
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.DELIVERED,
                      child: Text('Mark as Delivered'),
                    ),
                    const PopupMenuItem<OrderStatus>(
                      value: OrderStatus.CANCELLED,
                      child: Text('Mark as Cancelled'),
                    ),
                  ],
                  icon: const Icon(Icons.more_vert),
                ),
            ],
          ),
          body: viewModel.isLoading.state
              ? const Center(child: CircularProgressIndicator())
              : viewModel.order.state == null
                  ? const Center(child: Text('Order not found'))
                  : _buildOrderDetails(context, viewModel),
          bottomNavigationBar: viewModel.order.state != null &&
                  viewModel.order.state!.status != OrderStatus.DELIVERED &&
                  viewModel.order.state!.status != OrderStatus.CANCELLED
              ? _buildBottomBar(context, viewModel)
              : null,
        );
      },
    );
  }

  Widget _buildOrderDetails(
      BuildContext context, OrderDetailsScreenViewModel viewModel) {
    final order = viewModel.order.state!;
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹', decimalDigits: 2);

    return RefreshIndicator(
      onRefresh: () => viewModel.loadOrderDetails(),
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Order Status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Status',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      OrderStatusBadge(status: order.status),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Payment Status',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      PaymentStatusBadge(status: order.paymentStatus),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      PopupMenuButton<PaymentStatus>(
                        onSelected: (value) {
                          viewModel.updatePaymentStatus(value);
                        },
                        itemBuilder: (BuildContext context) => [
                          const PopupMenuItem<PaymentStatus>(
                            value: PaymentStatus.PENDING,
                            child: Text('Mark as Pending'),
                          ),
                          const PopupMenuItem<PaymentStatus>(
                            value: PaymentStatus.PAID,
                            child: Text('Mark as Paid'),
                          ),
                          const PopupMenuItem<PaymentStatus>(
                            value: PaymentStatus.FAILED,
                            child: Text('Mark as Failed'),
                          ),
                          const PopupMenuItem<PaymentStatus>(
                            value: PaymentStatus.REFUNDED,
                            child: Text('Mark as Refunded'),
                          ),
                        ],
                        child: TextButton.icon(
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Update Payment Status'),
                          onPressed: null,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 16),
                  Text('Created: ${dateFormat.format(order.createdAt)}'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Warehouse Information
          if (order.warehouse != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Warehouse Information',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.warehouse,
                            size: 16, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          order.warehouse!.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.category,
                            size: 16, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text('Type: ${order.warehouse!.type.name}'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.location_on,
                            size: 16, color: Colors.grey),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Location: ${order.warehouse!.lat}, ${order.warehouse!.long}',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    LocationMap(
                      latitude: order.warehouse!.lat,
                      longitude: order.warehouse!.long,
                      title: order.warehouse!.name,
                      height: 180,
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Delivery Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Delivery Information',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Date: ${dateFormat.format(order.deliveryDate)}'),
                  Text(
                      'Time: ${order.deliveryStartTime} - ${order.deliveryEndTime}'),
                  if (order.note != null && order.note!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Note:',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(order.note!),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Delivery Driver Information
          if (order.deliveryDriverId != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Delivery Driver',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (order.deliveryDriver != null) ...[
                      Row(
                        children: [
                          order.deliveryDriver!.profilePicture != null
                              ? CircleAvatar(
                                  backgroundImage: NetworkImage(
                                      order.deliveryDriver!.profilePicture!),
                                  radius: 25,
                                )
                              : const CircleAvatar(
                                  radius: 25,
                                  child: Icon(Icons.person),
                                ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  order.deliveryDriver!.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text('Phone: ${order.deliveryDriver!.phone}'),
                                Text('Email: ${order.deliveryDriver!.email}'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (order.deliveryDriver!.performance != null) ...[
                        const Divider(height: 24),
                        const Text(
                          'Driver Performance',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildPerformanceItem(
                              'Total Orders',
                              '${order.deliveryDriver!.performance!.totalOrderCount}',
                              Icons.shopping_bag,
                            ),
                            _buildPerformanceItem(
                              'Avg Pickup',
                              '${order.deliveryDriver!.performance!.avgPickupMins} mins',
                              Icons.access_time,
                            ),
                            _buildPerformanceItem(
                              'Avg Delivery',
                              '${order.deliveryDriver!.performance!.avgDeliveryMins} mins',
                              Icons.delivery_dining,
                            ),
                          ],
                        ),
                      ],
                    ] else
                      const Text(
                          'Driver assigned but details not available. Pull to refresh.'),
                    if (order.deliveryDriverAssignedDate != null) ...[
                      const SizedBox(height: 8),
                      Text(
                          'Assigned on: ${DateFormat('MMM dd, yyyy HH:mm').format(order.deliveryDriverAssignedDate!)}'),
                    ],
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Customer Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Customer Information',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Name: ${order.user?.name}'),
                  Text('Email: ${order.user?.email}'),
                  Text('Phone: ${order.user?.phone}'),

                  // Customer Metrics
                  if (order.user?.totalOrders != null ||
                      order.user?.totalOrderPrice != null ||
                      order.user?.averageOrderPrice != null) ...[
                    const SizedBox(height: 12),
                    const Divider(),
                    const SizedBox(height: 8),
                    const Text(
                      'Customer Metrics:',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    if (order.user?.totalOrders != null)
                      Row(
                        children: [
                          const Icon(Icons.shopping_bag,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Text('Total Orders: ${order.user!.totalOrders}'),
                        ],
                      ),
                    if (order.user?.totalOrderPrice != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.payments,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Text(
                              'Total Spent: ${currencyFormat.format(double.tryParse(order.user!.totalOrderPrice!) ?? 0)}'),
                        ],
                      ),
                    ],
                    if (order.user?.averageOrderPrice != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.trending_up,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Text(
                              'Average Order: ${currencyFormat.format(double.tryParse(order.user!.averageOrderPrice ?? "0") ?? 0)}'),
                        ],
                      ),
                    ],
                  ],

                  const SizedBox(height: 8),
                  const Text(
                    'Delivery Address:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(order.address.streetName),
                  Text(
                      '${order.address.city}, ${order.address.state} ${order.address.zipCode}'),
                  const SizedBox(height: 12),
                  LocationMap(
                    latitude: order.address.lat,
                    longitude: order.address.long,
                    title: 'Delivery Address',
                    height: 180,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Route Map
          if (order.warehouse != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Delivery Route',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 12),
                    RouteMap(
                      warehouseLat: order.warehouse!.lat,
                      warehouseLng: order.warehouse!.long,
                      customerLat: order.address.lat,
                      customerLng: order.address.long,
                      warehouseName: order.warehouse!.name,
                      customerAddress: order.address.streetName,
                      height: 300,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'The map shows both the warehouse (green) and delivery address (red) locations.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Order Items
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Order Items',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...order.items.map((item) =>
                      _buildOrderItemRow(context, item, currencyFormat)),
                  const Divider(),
                  _buildPriceSummary(context, order, currencyFormat),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItemRow(
      BuildContext context, OrderItem item, NumberFormat currencyFormat) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (item.variation != null && item.variation!.media.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Image.network(
                item.variation!.media.first.url,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 50,
                  height: 50,
                  color: Colors.grey.shade200,
                  child:
                      const Icon(Icons.image_not_supported, color: Colors.grey),
                ),
              ),
            )
          else if (item.product.media.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Image.network(
                item.product.media.first.url,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 50,
                  height: 50,
                  color: Colors.grey.shade200,
                  child:
                      const Icon(Icons.image_not_supported, color: Colors.grey),
                ),
              ),
            )
          else
            Container(
              width: 50,
              height: 50,
              color: Colors.grey.shade200,
              child: const Icon(Icons.image_not_supported, color: Colors.grey),
            ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (item.variation != null &&
                    item.variation!.options.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 8,
                    children: item.variation!.options.map((option) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (option.option.colorCode != null)
                              Container(
                                width: 12,
                                height: 12,
                                margin: const EdgeInsets.only(right: 4),
                                decoration: BoxDecoration(
                                  color: Color(int.parse(
                                      '0xFF${option.option.colorCode!.replaceAll('#', '')}')),
                                  shape: BoxShape.circle,
                                ),
                              ),
                            Text(
                              '${option.option.attribute.name}: ${option.option.name}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 4),
                ],
                if (item.inventory != null) ...[
                  Row(
                    children: [
                      const Icon(Icons.inventory_2,
                          size: 14, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'Inventory ID: ${item.inventory!.id}',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(Icons.batch_prediction,
                          size: 14, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'Batch: ${item.inventory!.batch.name}',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                ],
                Text('Quantity: ${item.quantity}'),
                if (double.tryParse(item.originalPrice) != null &&
                    double.tryParse(item.price) != null &&
                    double.parse(item.originalPrice) > double.parse(item.price))
                  Row(
                    children: [
                      Text(
                        currencyFormat.format(double.parse(item.originalPrice)),
                        style: const TextStyle(
                          decoration: TextDecoration.lineThrough,
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        currencyFormat.format(double.parse(item.price)),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  )
                else
                  Text(
                    currencyFormat.format(double.tryParse(item.price) ?? 0),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSummary(
      BuildContext context, Order order, NumberFormat currencyFormat) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Subtotal'),
            Text(currencyFormat.format(double.tryParse(order.subtotal) ?? 0)),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Handling Charge'),
            Text(currencyFormat
                .format(double.tryParse(order.handlingCharge) ?? 0)),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Delivery Fee'),
            Text(
                currencyFormat.format(double.tryParse(order.deliveryFee) ?? 0)),
          ],
        ),
        if (double.tryParse(order.couponDiscount) != null &&
            double.parse(order.couponDiscount) > 0) ...[
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Coupon Discount'),
              Text(
                  '-${currencyFormat.format(double.parse(order.couponDiscount))}'),
            ],
          ),
        ],
        if (double.tryParse(order.rewardDiscount) != null &&
            double.parse(order.rewardDiscount) > 0) ...[
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Reward Discount'),
              Text(
                  '-${currencyFormat.format(double.parse(order.rewardDiscount))}'),
            ],
          ),
        ],
        const SizedBox(height: 8),
        const Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Total',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            Text(
              currencyFormat.format(double.tryParse(order.totalAmount) ?? 0),
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ],
        ),
        // Show cash payment and change information if cashToPay > 0
        if (double.tryParse(order.cashToPay) != null &&
            double.parse(order.cashToPay) > 0) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.payments,
                        color: Colors.orange.shade700, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Cash Payment Details',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Customer Cash:'),
                    Text(
                      currencyFormat.format(double.parse(order.cashToPay)),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Order Total:'),
                    Text(
                      currencyFormat
                          .format(double.tryParse(order.totalAmount) ?? 0),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Divider(height: 1),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.change_circle,
                            color: Colors.green.shade700, size: 18),
                        const SizedBox(width: 4),
                        const Text(
                          'Change to Return:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        currencyFormat.format(double.parse(order.cashToPay) -
                            (double.tryParse(order.totalAmount) ?? 0)),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBottomBar(
      BuildContext context, OrderDetailsScreenViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: FilledButton.tonal(
              onPressed: () => viewModel.showAssignDriverDialog(context),
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Assign Driver'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: FilledButton(
              onPressed: () {
                final nextStatus =
                    _getNextStatus(viewModel.order.state!.status);
                if (nextStatus != null) {
                  viewModel.updateOrderStatus(nextStatus);
                }
              },
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child:
                  Text(_getNextStatusButtonText(viewModel.order.state!.status)),
            ),
          ),
        ],
      ),
    );
  }

  OrderStatus? _getNextStatus(OrderStatus currentStatus) {
    switch (currentStatus) {
      case OrderStatus.PENDING:
        return OrderStatus.CONFIRMED;
      case OrderStatus.CONFIRMED:
        return OrderStatus.READY;
      case OrderStatus.READY:
        return OrderStatus.SHIPPED;
      case OrderStatus.SHIPPED:
        return OrderStatus.DELIVERED;
      default:
        return null;
    }
  }

  String _getNextStatusButtonText(OrderStatus currentStatus) {
    switch (currentStatus) {
      case OrderStatus.PENDING:
        return 'Confirm Order';
      case OrderStatus.CONFIRMED:
        return 'Mark Ready';
      case OrderStatus.READY:
        return 'Mark Shipped';
      case OrderStatus.SHIPPED:
        return 'Mark Delivered';
      default:
        return 'Update Status';
    }
  }

  Widget _buildPerformanceItem(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
