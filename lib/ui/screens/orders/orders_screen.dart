import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_warehouse/domain/model/order.dart';
import 'package:vegmove_warehouse/ui/components/order_status_badge.dart';
import 'package:vegmove_warehouse/ui/components/payment_status_badge.dart';
import 'package:vegmove_warehouse/ui/screens/orders/orders_screen_viewmodel.dart';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  final ScrollController _scrollController = ScrollController();
  late OrdersScreenViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_viewModel.isLoading.state) {
      _viewModel.loadMoreOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<OrdersScreenViewModel>(
      viewModelBuilder: () {
        _viewModel = OrdersScreenViewModel();
        return _viewModel;
      },
      builder: (context, viewModel) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Orders'),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) {
                  viewModel.filterByStatus(value);
                },
                itemBuilder: (BuildContext context) => [
                  const PopupMenuItem<String>(
                    value: 'ALL',
                    child: Text('All Orders'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'PENDING',
                    child: Text('Pending'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'CONFIRMED',
                    child: Text('Confirmed'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'READY',
                    child: Text('Ready'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'SHIPPED',
                    child: Text('Shipped'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'DELIVERED',
                    child: Text('Delivered'),
                  ),
                  const PopupMenuItem<String>(
                    value: 'CANCELLED',
                    child: Text('Cancelled'),
                  ),
                ],
                icon: const Icon(Icons.filter_list),
              ),
            ],
          ),
          body: RefreshIndicator(
            onRefresh: () => viewModel.loadOrders(),
            child: viewModel.isLoading.state
                ? const Center(child: CircularProgressIndicator())
                : viewModel.orders.isEmpty
                    ? Center(
                        child: Text(
                          'No orders found',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      )
                    : Column(
                        children: [
                          Expanded(
                            child: ListView.builder(
                              controller: _scrollController,
                              itemCount: viewModel.orders.length +
                                  (viewModel.paginationData.state.page <
                                          viewModel
                                              .paginationData.state.totalPages
                                      ? 1
                                      : 0),
                              itemBuilder: (context, index) {
                                if (index == viewModel.orders.length) {
                                  // This is the loading indicator at the bottom
                                  return const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }
                                final order = viewModel.orders[index];
                                return _buildOrderCard(context, order);
                              },
                            ),
                          ),
                        ],
                      ),
          ),
        );
      },
    );
  }

  Widget _buildOrderCard(BuildContext context, Order order) {
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹', decimalDigits: 2);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          GoRouter.of(context).push('/order-details/${order.id}');
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Order #${order.id}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  OrderStatusBadge(status: order.status),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.calendar_today,
                      size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Ordered: ${dateFormat.format(order.createdAt)}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.access_time, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Delivery: ${dateFormat.format(order.deliveryDate)} ${order.deliveryStartTime} - ${order.deliveryEndTime}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.person, size: 16),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      order.user?.name ?? '',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${order.address.streetName}, ${order.address.city}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              if (order.warehouse != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.warehouse, size: 16),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'Warehouse: ${order.warehouse!.name} (${order.warehouse!.type.name})',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.payment, size: 16),
                  const SizedBox(width: 4),
                  PaymentStatusBadge(status: order.paymentStatus),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${order.items.length} items',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    currencyFormat
                        .format(double.tryParse(order.totalAmount) ?? 0),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
