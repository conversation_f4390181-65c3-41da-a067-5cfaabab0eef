import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_delivery/domain/model/order.dart';
import 'package:vegmove_delivery/network/api.dart';
import 'package:vegmove_delivery/providers/location_tracker_provider.dart';
import 'package:vegmove_delivery/services/background_location_service.dart';
import 'package:vegmove_delivery/util.dart';
import 'package:viewmodel_flutter/flutter_viewmodel.dart';
import 'package:viewmodel_flutter/states.dart';

class OrderDetailsViewModel extends ViewModel {
  final int orderId;
  final Api api = Api();

  late MutableState<Order?> order;
  late MutableState<bool> isLoading;

  OrderDetailsViewModel({required this.orderId});

  @override
  void init() {
    order = mutableStateOf(null);
    isLoading = mutableStateOf(true);
    fetchOrderDetails();
  }

  Future<void> fetchOrderDetails() async {
    isLoading(true);
    try {
      final response = await api.get('delivery-drivers/orders/$orderId');

      if (response == null) {
        Util.showSomethingWentWrongToast();
        return;
      }

      order(orderFromJson(response));
    } catch (e) {
      Util.showErrorToast('Failed to load order: $e');
    } finally {
      isLoading(false);
    }
  }

  Future<void> markAsShipped() async {
    try {
      await api.patch('delivery-drivers/orders/$orderId/ship');
      await fetchOrderDetails();

      // Update location tracking frequency for shipping order
      _updateLocationTrackingForShipping(true);

      Util.showSuccessToast('Marked as shipped');
    } catch (e) {
      Util.showErrorToast('Failed to update: $e');
    }
  }

  // Update location tracking frequency based on shipping status
  void _updateLocationTrackingForShipping(bool isShipping) {
    try {
      // Get the container to access providers
      final container = ProviderContainer();

      // Update foreground location tracking
      container
          .read(locationTrackerProvider.notifier)
          .updateShippingOrderStatus(isShipping);

      // Update background service if running
      BackgroundLocationService.updateShippingOrderStatus(isShipping);

      print('Location tracking frequency updated for shipping: $isShipping');
    } catch (e) {
      print('Error updating location tracking frequency: $e');
    }
  }

  Future<void> markAsDelivered() async {
    try {
      await api.patch('delivery-drivers/orders/$orderId/deliver');
      await fetchOrderDetails();

      // Reset location tracking frequency after delivery
      _updateLocationTrackingForShipping(false);

      Util.showSuccessToast('Marked as delivered');
    } catch (e) {
      Util.showErrorToast('Failed to update: $e');
    }
  }
}
