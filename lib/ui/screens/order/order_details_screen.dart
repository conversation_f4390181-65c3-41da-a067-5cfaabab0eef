import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_delivery/domain/model/order.dart';
import 'package:vegmove_delivery/ui/components/countdown_timer.dart';
import 'package:vegmove_delivery/ui/components/route_map.dart';
import 'package:vegmove_delivery/ui/screens/order/order_details_screen_viewmodel.dart';
import 'package:viewmodel_flutter/viewmodel_builder.dart';

class OrderDetailsScreen extends StatelessWidget {
  final int orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  String _formatDateTime(DateTime date, String startTime, String endTime) {
    return '${DateFormat.yMMMd().format(date)} between $startTime and $endTime';
  }

  /// Generic section builder that wraps content in a Card with a header.
  Widget _buildSection({
    required BuildContext context,
    required IconData headerIcon,
    required String headerTitle,
    required Widget content,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(headerIcon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  headerTitle,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 20, thickness: 1),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoSection(BuildContext context, order) {
    return _buildSection(
      context: context,
      headerIcon: Icons.person,
      headerTitle: 'Customer Info',
      content: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: const Icon(Icons.person_outline),
            title: Text(order.user.name,
                style: const TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Row(
              children: [
                const Icon(Icons.phone, size: 16),
                const SizedBox(width: 4),
                Text(order.user.email),
              ],
            ),
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: const Icon(Icons.email_outlined),
            title: Text(order.user.email),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseSection(BuildContext context, order) {
    final warehouse = order.items.isNotEmpty
        ? order.items.first.inventory.batch.warehouse
        : null;
    final double? warehouseLat =
        warehouse != null ? double.tryParse(warehouse.lat) : null;
    final double? warehouseLng =
        warehouse != null ? double.tryParse(warehouse.long) : null;

    return _buildSection(
      context: context,
      headerIcon: Icons.store,
      headerTitle: 'Pickup Warehouse',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.location_city, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  warehouse?.name ?? 'Unknown Warehouse',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),

          // Add Google Map for warehouse location
          if (warehouseLat != null && warehouseLng != null) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 180,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(warehouseLat, warehouseLng),
                    zoom: 15,
                  ),
                  markers: {
                    Marker(
                      markerId: const MarkerId('warehouse_location'),
                      position: LatLng(warehouseLat, warehouseLng),
                      infoWindow: InfoWindow(
                        title: 'Warehouse',
                        snippet: warehouse?.name ?? '',
                      ),
                      icon: BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueGreen),
                    ),
                  },
                  zoomControlsEnabled: true,
                  scrollGesturesEnabled: true,
                  tiltGesturesEnabled: false,
                  rotateGesturesEnabled: true,
                  mapType: MapType.normal,
                ),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Warehouse pickup location',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddressSection(BuildContext context, order) {
    final double? latitude = double.tryParse(order.address.lat);
    final double? longitude = double.tryParse(order.address.long);

    return _buildSection(
      context: context,
      headerIcon: Icons.home,
      headerTitle: 'Delivery Address',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: const Icon(Icons.location_on),
            title: Text(order.address.streetName ?? ''),
            subtitle: Text('${order.address.city}, ${order.address.state}'),
          ),
          const SizedBox(height: 12),
          if (latitude != null && longitude != null)
            SizedBox(
              height: 200,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(latitude, longitude),
                    zoom: 15,
                  ),
                  markers: {
                    Marker(
                      markerId: const MarkerId('delivery_location'),
                      position: LatLng(latitude, longitude),
                      infoWindow: const InfoWindow(title: 'Delivery Address'),
                      icon: BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueRed),
                    ),
                  },
                  zoomControlsEnabled: true,
                  scrollGesturesEnabled: true,
                  tiltGesturesEnabled: false,
                  rotateGesturesEnabled: true,
                  mapType: MapType.normal,
                ),
              ),
            )
          else
            const Text('Location not available'),

          // Add route map if we have warehouse info and we're in SHIPPED status
          if (order.status == 'SHIPPED' &&
              latitude != null &&
              longitude != null &&
              order.items.isNotEmpty &&
              double.tryParse(
                      order.items.first.inventory.batch.warehouse.lat) !=
                  null &&
              double.tryParse(
                      order.items.first.inventory.batch.warehouse.long) !=
                  null) ...[
            const SizedBox(height: 24),
            const Text(
              'Delivery Route',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            RouteMap(
              warehouseLat: order.items.first.inventory.batch.warehouse.lat,
              warehouseLng: order.items.first.inventory.batch.warehouse.long,
              customerLat: order.address.lat,
              customerLng: order.address.long,
              warehouseName: order.items.first.inventory.batch.warehouse.name,
              customerAddress: order.address.streetName ?? '',
              height: 250,
            ),
            const SizedBox(height: 8),
            const Text(
              'The map shows both the warehouse (green) and delivery address (red) locations.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeliveryScheduleSection(BuildContext context, order) {
    return _buildSection(
      context: context,
      headerIcon: Icons.schedule,
      headerTitle: 'Delivery Schedule',
      content: Row(
        children: [
          const Icon(Icons.calendar_today, size: 20),
          const SizedBox(width: 8),
          Text(_formatDateTime(order.deliveryDate, order.deliveryStartTime,
              order.deliveryEndTime)),
        ],
      ),
    );
  }

  Widget _buildCountdownSection(BuildContext context, order) {
    // If no countdown timers are available, return empty widget
    if ((order.status != 'READY' || order.pickupDeadline == null) &&
        (order.status != 'SHIPPED' || order.deliveryDeadline == null)) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timer, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Delivery Timer',
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 20, thickness: 1),

            // Show countdown timers if available
            if (order.status == 'READY' && order.pickupDeadline != null) ...[
              CountdownTimer(
                targetTime: order.pickupDeadline!,
                label: 'Time to pickup from warehouse',
                icon: Icons.access_time,
                backgroundColor: Colors.amber.shade100,
                textColor: Colors.amber.shade900,
              ),
            ],

            if (order.status == 'SHIPPED' &&
                order.deliveryDeadline != null) ...[
              CountdownTimer(
                targetTime: order.deliveryDeadline!,
                label: 'Time to deliver',
                icon: Icons.delivery_dining,
                backgroundColor: Colors.green.shade100,
                textColor: Colors.green.shade900,
              ),
              if (order.travelTime != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.directions_bike, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Estimated travel time: ${order.travelTime} minutes',
                        style: TextStyle(
                          color: Colors.blue.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTotalPriceSection(BuildContext context, order) {
    return _buildSection(
      context: context,
      headerIcon: Icons.attach_money,
      headerTitle: 'Total Price',
      content: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Price:',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              Text(
                '₹${order.totalAmount}',
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
          if (double.tryParse(order.cashToPay) != null &&
              double.parse(order.cashToPay) > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.payments,
                          color: Colors.amber.shade800, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Cash Payment',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.amber.shade800,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Cash to collect:'),
                      Text(
                        '₹${order.cashToPay}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Change needed:'),
                      Text(
                        '₹${(double.parse(order.cashToPay) - double.parse(order.totalAmount)).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNoteSection(BuildContext context, order) {
    if ((order.note ?? '').trim().isEmpty) {
      return const SizedBox.shrink();
    }
    return _buildSection(
      context: context,
      headerIcon: Icons.note,
      headerTitle: 'Note',
      content: Text(order.note),
    );
  }

  Widget _buildOrderedItemsSection(BuildContext context, Order order) {
    return _buildSection(
      context: context,
      headerIcon: Icons.shopping_bag,
      headerTitle: 'Ordered Items',
      content: Column(
        children: order.items.map<Widget>((item) {
          // Build variation details if available
          // Create variation chips to be displayed under product name
          final List<Widget> variationChips = [];
          if (item.variation != null && item.variation!.options.isNotEmpty) {
            for (var option in item.variation!.options) {
              final attributeName = option.option.attribute.name;
              final optionName = option.option.name;
              final colorCode = option.option.colorCode;

              variationChips.add(
                Container(
                  margin: const EdgeInsets.only(right: 8, top: 4),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (colorCode != null &&
                          colorCode.isNotEmpty &&
                          attributeName.toLowerCase() == 'color') ...[
                        Container(
                          width: 12,
                          height: 12,
                          margin: const EdgeInsets.only(right: 4),
                          decoration: BoxDecoration(
                            color: Color(int.parse(
                                '0xFF${colorCode.replaceAll('#', '')}')),
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                        ),
                      ],
                      Text(
                        '$attributeName: $optionName',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
          }

          debugPrint('here I am');
          debugPrint('${item.product.thumbnail?.url}');

          return Card(
            margin: const EdgeInsets.symmetric(vertical: 8),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 2,
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product header with image
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product image
                    Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      child: item.product.thumbnail != null
                          ? Image.network(
                              item.product.thumbnail!.url,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(
                                    Icons.image_not_supported_outlined,
                                    color: Colors.grey,
                                    size: 30,
                                  ),
                                );
                              },
                            )
                          : const Center(
                              child: Icon(
                                Icons.image_outlined,
                                color: Colors.grey,
                                size: 30,
                              ),
                            ),
                    ),
                    // Product details
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.product.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            if (variationChips.isNotEmpty)
                              Wrap(
                                children: variationChips,
                              ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withAlpha(25),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'Qty: ${item.quantity}',
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '₹${item.price}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                // Divider
                const Divider(height: 1),

                // Additional details
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.inventory_2_outlined,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Batch: ${item.inventory.batch.name}',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontSize: 13,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.store_outlined,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Warehouse: ${item.inventory.batch.warehouse.name}',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontSize: 13,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // No variation details here anymore
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionSection(
      BuildContext context, order, OrderDetailsViewModel vm) {
    if (order.status == 'READY') {
      return ElevatedButton.icon(
        onPressed: vm.markAsShipped,
        icon: const Icon(Icons.local_shipping),
        label: const Text('Mark as Received from Warehouse'),
        style: ElevatedButton.styleFrom(
          minimumSize: const Size.fromHeight(48),
        ),
      );
    } else if (order.status == 'SHIPPED') {
      return ElevatedButton.icon(
        onPressed: vm.markAsDelivered,
        icon: const Icon(Icons.delivery_dining),
        label: const Text('Mark as Delivered'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          minimumSize: const Size.fromHeight(48),
        ),
      );
    } else {
      return Center(
        child: Text(
          'No further actions available',
          style:
              TextStyle(fontStyle: FontStyle.italic, color: Colors.grey[600]),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<OrderDetailsViewModel>(
      viewModelBuilder: () => OrderDetailsViewModel(orderId: orderId),
      builder: (context, vm) {
        final order = vm.order.state;
        return Scaffold(
          appBar: AppBar(
            title: Text('Order #$orderId'),
          ),
          body: vm.isLoading.state
              ? const Center(child: CircularProgressIndicator())
              : order == null
                  ? const Center(child: Text('Order not found'))
                  : ListView(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      children: [
                        // Countdown timer at the top for high visibility
                        _buildCountdownSection(context, order),
                        _buildCustomerInfoSection(context, order),
                        _buildWarehouseSection(context, order),
                        _buildAddressSection(context, order),
                        _buildDeliveryScheduleSection(context, order),
                        _buildTotalPriceSection(context, order),
                        _buildNoteSection(context, order),
                        _buildOrderedItemsSection(context, order),
                        const SizedBox(height: 24),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: _buildActionSection(context, order, vm),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
        );
      },
    );
  }
}
