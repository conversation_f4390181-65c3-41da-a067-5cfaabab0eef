import 'package:flutter/material.dart';
import 'package:vegmove_warehouse/domain/model/enums.dart';

class PaymentStatusBadge extends StatelessWidget {
  final PaymentStatus status;

  const PaymentStatusBadge({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    
    switch (status) {
      case PaymentStatus.PAID:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case PaymentStatus.PENDING:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case PaymentStatus.FAILED:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        break;
      case PaymentStatus.REFUNDED:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status.name,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}
