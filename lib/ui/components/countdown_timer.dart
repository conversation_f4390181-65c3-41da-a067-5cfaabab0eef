import 'dart:async';
import 'package:flutter/material.dart';

class CountdownTimer extends StatefulWidget {
  final DateTime targetTime;
  final String label;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final VoidCallback? onComplete;

  const CountdownTimer({
    super.key,
    required this.targetTime,
    required this.label,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.onComplete,
  });

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer> {
  late Timer _timer;
  late Duration _remainingTime;
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _calculateRemainingTime();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _calculateRemainingTime() {
    final now = DateTime.now();
    if (widget.targetTime.isAfter(now)) {
      _remainingTime = widget.targetTime.difference(now);
    } else {
      _remainingTime = Duration.zero;
      _isCompleted = true;
      if (widget.onComplete != null) {
        widget.onComplete!();
      }
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _calculateRemainingTime();
          if (_remainingTime == Duration.zero) {
            _timer.cancel();
          }
        });
      }
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return '$hours:$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final Color bgColor = widget.backgroundColor ?? Colors.blue.shade100;
    final Color txtColor = widget.textColor ?? Colors.blue.shade900;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          if (widget.icon != null) ...[
            Icon(widget.icon, color: txtColor),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.label,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: txtColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isCompleted
                      ? 'Time expired!'
                      : _formatDuration(_remainingTime),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    color: _isCompleted ? Colors.red : txtColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
