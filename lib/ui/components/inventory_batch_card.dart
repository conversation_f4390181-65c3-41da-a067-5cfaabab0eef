import 'package:flutter/material.dart';
import 'package:vegmove_warehouse/domain/model/inventory_batch.dart';

class InventoryBatchCard extends StatelessWidget {
  final InventoryBatch batch;

  const InventoryBatchCard({
    super.key,
    required this.batch,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  batch.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'ID: ${batch.id}',
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.warehouse, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'Warehouse: ${batch.warehouse.name}',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            // const SizedBox(height: 8),
            // Row(
            //   children: [
            //     const Icon(Icons.location_on, size: 16, color: Colors.grey),
            //     const SizedBox(width: 8),
            //     Text(
            //       'Location: ${batch.warehouse.lat}, ${batch.warehouse.long}',
            //       style: const TextStyle(
            //         fontSize: 14,
            //         color: Colors.grey,
            //       ),
            //     ),
            //   ],
            // ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.category, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'Type: ${batch.warehouse.type.name}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Created: ${_formatDate(batch.createdAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  'Updated: ${_formatDate(batch.updatedAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.end,
            //   children: [
            //     TextButton.icon(
            //       onPressed: () {
            //         // View details logic
            //       },
            //       icon: const Icon(Icons.visibility),
            //       label: const Text('View'),
            //     ),
            //     TextButton.icon(
            //       onPressed: () {
            //         // Edit logic
            //       },
            //       icon: const Icon(Icons.edit),
            //       label: const Text('Edit'),
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
