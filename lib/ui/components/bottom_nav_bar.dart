import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;

  const BottomNavBar({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextButton.icon(
            onPressed: () {
              if (currentIndex != 0) {
                GoRouter.of(context).go('/home');
              }
            },
            icon: const Icon(Icons.inventory),
            label: const Text('Inventory'),
            style: TextButton.styleFrom(
              foregroundColor: currentIndex == 0
                  ? Theme.of(context).primaryColor
                  : Colors.grey,
            ),
          ),
          TextButton.icon(
            onPressed: () {
              if (currentIndex != 1) {
                GoRouter.of(context).go('/orders');
              }
            },
            icon: const Icon(Icons.shopping_bag),
            label: const Text('Orders'),
            style: TextButton.styleFrom(
              foregroundColor: currentIndex == 1
                  ? Theme.of(context).primaryColor
                  : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
