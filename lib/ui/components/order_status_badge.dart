import 'package:flutter/material.dart';
import 'package:vegmove_warehouse/domain/model/enums.dart';

class OrderStatusBadge extends StatelessWidget {
  final OrderStatus status;

  const OrderStatusBadge({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor = Colors.white;

    switch (status) {
      case OrderStatus.PENDING:
        backgroundColor = Colors.orange;
        break;
      case OrderStatus.CONFIRMED:
        backgroundColor = Colors.blue;
        break;
      case OrderStatus.PACKING:
        backgroundColor = Colors.brown;
        break;
      case OrderStatus.READY:
        backgroundColor = Colors.purple;
        break;
      case OrderStatus.SHIPPED:
        backgroundColor = Colors.indigo;
        break;
      case OrderStatus.DELIVERED:
        backgroundColor = Colors.green;
        break;
      case OrderStatus.CANCELLED:
        backgroundColor = Colors.red;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.name,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
