import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteMap extends StatefulWidget {
  final String warehouseLat;
  final String warehouseLng;
  final String customerLat;
  final String customerLng;
  final String warehouseName;
  final String customerAddress;
  final double height;
  final bool interactive;

  const RouteMap({
    super.key,
    required this.warehouseLat,
    required this.warehouseLng,
    required this.customerLat,
    required this.customerLng,
    required this.warehouseName,
    required this.customerAddress,
    this.height = 300,
    this.interactive = true,
  });

  @override
  State<RouteMap> createState() => _RouteMapState();
}

class _RouteMapState extends State<RouteMap> {
  GoogleMapController? _mapController;
  LatLngBounds? _bounds;

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double? warehouseLat = double.tryParse(widget.warehouseLat);
    final double? warehouseLng = double.tryParse(widget.warehouseLng);
    final double? customerLat = double.tryParse(widget.customerLat);
    final double? customerLng = double.tryParse(widget.customerLng);

    if (warehouseLat == null ||
        warehouseLng == null ||
        customerLat == null ||
        customerLng == null) {
      return SizedBox(
        height: widget.height,
        child: const Center(
          child: Text('Invalid location coordinates'),
        ),
      );
    }

    final warehousePosition = LatLng(warehouseLat, warehouseLng);
    final customerPosition = LatLng(customerLat, customerLng);

    // Create markers
    final Set<Marker> markers = {
      Marker(
        markerId: const MarkerId('warehouse'),
        position: warehousePosition,
        infoWindow:
            InfoWindow(title: widget.warehouseName, snippet: 'Pickup location'),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      ),
      Marker(
        markerId: const MarkerId('customer'),
        position: customerPosition,
        infoWindow: InfoWindow(
            title: 'Delivery Address', snippet: widget.customerAddress),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      ),
    };

    return SizedBox(
      height: widget.height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _calculateCenter(warehousePosition, customerPosition),
                zoom: 12,
              ),
              markers: markers,
              zoomControlsEnabled: widget.interactive,
              scrollGesturesEnabled: widget.interactive,
              tiltGesturesEnabled: widget.interactive,
              rotateGesturesEnabled: widget.interactive,
              mapType: MapType.normal,
              myLocationButtonEnabled: false,
              onMapCreated: (controller) {
                _mapController = controller;
                _fitBounds(warehousePosition, customerPosition);
              },
            ),
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton.small(
                onPressed: () {
                  if (_bounds != null) {
                    _mapController?.animateCamera(
                      CameraUpdate.newLatLngBounds(_bounds!, 50),
                    );
                  }
                },
                backgroundColor: Colors.white,
                child:
                    const Icon(Icons.center_focus_strong, color: Colors.black),
              ),
            ),
          ],
        ),
      ),
    );
  }

  LatLng _calculateCenter(LatLng position1, LatLng position2) {
    return LatLng(
      (position1.latitude + position2.latitude) / 2,
      (position1.longitude + position2.longitude) / 2,
    );
  }

  void _fitBounds(LatLng position1, LatLng position2) {
    if (_mapController == null) return;

    final double minLat = position1.latitude < position2.latitude
        ? position1.latitude
        : position2.latitude;
    final double maxLat = position1.latitude > position2.latitude
        ? position1.latitude
        : position2.latitude;
    final double minLng = position1.longitude < position2.longitude
        ? position1.longitude
        : position2.longitude;
    final double maxLng = position1.longitude > position2.longitude
        ? position1.longitude
        : position2.longitude;

    // Add some padding
    final double latPadding = (maxLat - minLat) * 0.2;
    final double lngPadding = (maxLng - minLng) * 0.2;

    _bounds = LatLngBounds(
      southwest: LatLng(minLat - latPadding, minLng - lngPadding),
      northeast: LatLng(maxLat + latPadding, maxLng + lngPadding),
    );

    _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(_bounds!, 50),
    );
  }
}
