import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationMap extends StatelessWidget {
  final String latitude;
  final String longitude;
  final String? title;
  final double height;
  final bool interactive;

  const LocationMap({
    super.key,
    required this.latitude,
    required this.longitude,
    this.title,
    this.height = 200,
    this.interactive = false,
  });

  @override
  Widget build(BuildContext context) {
    final double? lat = double.tryParse(latitude);
    final double? lng = double.tryParse(longitude);

    if (lat == null || lng == null) {
      return SizedBox(
        height: height,
        child: const Center(
          child: Text('Invalid location coordinates'),
        ),
      );
    }

    return SizedBox(
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: GoogleMap(
          initialCameraPosition: CameraPosition(
            target: LatLng(lat, lng),
            zoom: 15,
          ),
          markers: {
            Marker(
              markerId: MarkerId(title ?? 'location'),
              position: LatLng(lat, lng),
              infoWindow: title != null ? InfoWindow(title: title!) : InfoWindow.noText,
            ),
          },
          zoomControlsEnabled: interactive,
          scrollGesturesEnabled: interactive,
          tiltGesturesEnabled: interactive,
          rotateGesturesEnabled: interactive,
          mapType: MapType.normal,
          myLocationButtonEnabled: false,
        ),
      ),
    );
  }
}
