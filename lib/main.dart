import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_warehouse/ui/screens/auth/login_screen.dart';
import 'package:vegmove_warehouse/ui/screens/initialize/initialize_screen.dart';
import 'package:vegmove_warehouse/ui/screens/inventory/add_inventory_screen.dart';
import 'package:vegmove_warehouse/ui/screens/main/main_screen.dart';
import 'package:vegmove_warehouse/ui/screens/orders/delivery_driver_selection_screen.dart';
import 'package:vegmove_warehouse/ui/screens/orders/order_details_screen.dart';
import 'package:vegmove_warehouse/ui/screens/splash/splash_screen.dart';

import 'notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await NotificationService.init();
  FirebaseMessaging.onBackgroundMessage(_backgroundHandler);
  runApp(ProviderScope(child: MyApp()));
}

Future<void> _backgroundHandler(RemoteMessage message) async {
  // Handle background message
  await NotificationService.show(message);
}

class MyApp extends StatefulWidget {
  MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _router = GoRouter(
    initialLocation: "/",
    routes: [
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => LoginScreen(),
      ),
      GoRoute(
        path: '/initialize',
        name: 'initialize',
        builder: (context, state) => InitializeScreen(),
      ),
      GoRoute(
        path: '/main',
        name: 'main',
        builder: (context, state) => const MainScreen(),
      ),
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const MainScreen(initialIndex: 0),
      ),
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const MainScreen(initialIndex: 1),
      ),
      GoRoute(
        path: '/add-inventory',
        name: 'add-inventory',
        builder: (context, state) => AddInventoryPage(),
      ),
      GoRoute(
        path: '/order-details/:id',
        name: 'order-details',
        builder: (context, state) {
          final orderId = int.parse(state.pathParameters['id']!);
          return OrderDetailsScreen(orderId: orderId);
        },
      ),
      GoRoute(
        path: '/delivery-driver-selection',
        name: 'delivery-driver-selection',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final warehouseId = extra?['warehouseId'] as int?;
          return DeliveryDriverSelectionScreen(warehouseId: warehouseId);
        },
      ),
    ],
  );

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  @override
  void initState() {
    super.initState();
    _setupFirebaseMessaging();
    _checkForInitialNotification();
  }

  Future<void> _setupFirebaseMessaging() async {
    await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // Get the token
    String? token = await _firebaseMessaging.getToken();
    print('Firebase Token: $token');

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Message data: ${message.data}');
      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
      }

      NotificationService.show(message);
    });

    // Handle when app is opened from a notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('Message clicked! ${message.messageId}');
      _handleNotificationNavigation(message.data);
    });

    // Handle notification tap from local notification
    if (NotificationService.latestNotificationData != null) {
      _handleNotificationNavigation(
          NotificationService.latestNotificationData!);
    }
  }

  Future<void> _checkForInitialNotification() async {
    // Check if app was opened from a notification
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      print('App opened from terminated state via notification');
      _handleNotificationNavigation(initialMessage.data);
    }
  }

  void _handleNotificationNavigation(Map<String, dynamic> data) {
    final screen = data['screen'];
    final orderId = int.tryParse(data['orderId'] ?? '');

    if (screen == 'order-details' && orderId != null) {
      _router.push('/order-details/$orderId');
    } else if (screen == 'orders') {
      _router.push('/orders');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Flutter Demo',
      theme: ThemeData(
        primaryColor: Colors.deepPurple,
        scaffoldBackgroundColor: Colors.white,
        useMaterial3: false,
      ),
      routerConfig: _router,
    );
  }
}
