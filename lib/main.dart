import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_delivery/providers/location_tracker_provider.dart';
import 'package:vegmove_delivery/services/background_location_service.dart';
import 'package:vegmove_delivery/ui/screens/auth/login_screen.dart';
import 'package:vegmove_delivery/ui/screens/main/main_navigation_screen.dart';
import 'package:vegmove_delivery/ui/screens/initialize/initialize_screen.dart';
import 'package:vegmove_delivery/ui/screens/order/order_details_screen.dart';
import 'package:vegmove_delivery/ui/screens/splash/splash_screen.dart';

import 'notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize notification service
  await NotificationService.init();

  // Initialize background location service
  await BackgroundLocationService.init();

  // Initialize shared preferences with default values for location tracking
  final prefs = await SharedPreferences.getInstance();
  if (!prefs.containsKey('normal_interval_seconds')) {
    await prefs.setInt('normal_interval_seconds', 30);
  }
  if (!prefs.containsKey('shipping_interval_seconds')) {
    await prefs.setInt('shipping_interval_seconds', 10);
  }

  FirebaseMessaging.onBackgroundMessage(_backgroundHandler);
  runApp(ProviderScope(child: MyApp()));
}

Future<void> _backgroundHandler(RemoteMessage message) async {
  // Handle background message
  await NotificationService.show(message);
}

class MyApp extends ConsumerStatefulWidget {
  MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  final _router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => LoginScreen(),
      ),
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const MainNavigationScreen(),
      ),
      GoRoute(
        name: 'orderDetails',
        path: '/order/:id',
        builder: (context, state) {
          final orderId = int.parse(state.pathParameters['id']!);
          return OrderDetailsScreen(orderId: orderId);
        },
      ),
      GoRoute(
        path: '/initialize',
        name: 'initialize',
        builder: (context, state) => InitializeScreen(),
      ),
    ],
  );

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  @override
  void initState() {
    super.initState();

    // Initialize Firebase messaging
    _firebaseMessaging.requestPermission();
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Message data: ${message.data}');
      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
      }

      NotificationService.show(message);

      // Check if the message is about an order status change
      if (message.data.containsKey('orderStatus') &&
          message.data['orderStatus'] == 'SHIPPED') {
        // Update location tracking for shipping order
        ref
            .read(locationTrackerProvider.notifier)
            .updateShippingOrderStatus(true);

        // Update background service if running
        BackgroundLocationService.updateShippingOrderStatus(true);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('Message clicked! ${message.messageId}');
      // final screen = message.data['screen'];
      // final orderId = int.tryParse(message.data['orderId'] ?? '');
      //
      // if (screen == 'order-details' && orderId != null) {
      //   context.pushNamed('order-details', extra: orderId);
      // }
    });

    // Initialize location tracking when the app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check if user is logged in before starting location tracking
      SharedPreferences.getInstance().then((prefs) {
        final token = prefs.getString('token');
        if (token != null && token.isNotEmpty) {
          // Start location tracking
          ref.read(locationTrackerProvider.notifier).startTracking();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // Access location tracker provider to ensure it's initialized
    ref.watch(locationTrackerProvider);

    return MaterialApp.router(
      title: 'Vegmove Delivery',
      theme: ThemeData(
        primaryColor: Colors.deepPurple,
        scaffoldBackgroundColor: Colors.white,
        useMaterial3: false,
      ),
      routerConfig: _router,
    );
  }
}
