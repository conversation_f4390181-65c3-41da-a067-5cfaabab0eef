import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Api {
  static final Api _api = Api._internal();
  late final Dio _dio;
  late SharedPreferences _sharedPreferences;

  factory Api() {
    return _api;
  }

  Api._internal() {
    SharedPreferences.getInstance().then((value) {
      _sharedPreferences = value;
    });
    var baseUrl = '';
    if (Platform.isAndroid) {
      baseUrl = 'http://10.0.2.2:3000/';
    } else {
      baseUrl = 'http://127.0.0.1:3000/';
    }

    baseUrl = 'https://vegmove-backend.shq.su/';

    final options = BaseOptions(
      baseUrl: baseUrl,
      responseType: ResponseType.plain,
      validateStatus: (status) {
        return true; // All status codes are considered valid.
      },
    );
    _dio = Dio(options);
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers['Authorization'] =
              'Bearer ${_sharedPreferences.get("token")}';
          return handler.next(options);
        },
      ),
    );

    _dio.interceptors.add(LogInterceptor(
      responseBody: true,
      requestBody: true,
    ));
  }

  Future<String?> get(
    String endpoint, {
    Map<String, dynamic>? queries,
  }) async {
    try {
      Response response = await _dio.get(
        endpoint,
        queryParameters: queries,
      );
      return response.data;
    } catch (exception) {
      log(exception.toString());
      return null;
    }
  }

  Future<String?> post(
    String endpoint, {
    Map<String, dynamic>? data,
    FormData? formData,
  }) async {
    try {
      Response response = await _dio.post(
        endpoint,
        data: data ?? formData,
      );
      return response.data;
    } catch (exception) {
      log(exception.toString());
      return null;
    }
  }

  Future<String?> patch(
    String endpoint, {
    Map<String, dynamic>? data,
  }) async {
    try {
      Response response = await _dio.patch(
        endpoint,
        data: data,
      );
      return response.data;
    } catch (exception) {
      log(exception.toString());
      return null;
    }
  }

  Future<String?> delete(
    String endpoint, {
    Map<String, dynamic>? queries,
    Map<String, dynamic>? data,
  }) async {
    try {
      Response response = await _dio.delete(
        endpoint,
        queryParameters: queries,
        data: data,
      );
      return response.data;
    } catch (exception) {
      log(exception.toString());
      return null;
    }
  }
}
