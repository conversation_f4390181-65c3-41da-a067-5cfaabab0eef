import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/user.dart';

DeliveryDriversResponse deliveryDriversResponseFromJson(String str) =>
    DeliveryDriversResponse.fromJson(json.decode(str));

String deliveryDriversResponseToJson(DeliveryDriversResponse data) =>
    json.encode(data.toJson());

class DeliveryDriversResponse {
  final List<User> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  DeliveryDriversResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory DeliveryDriversResponse.fromJson(Map<String, dynamic> json) =>
      DeliveryDriversResponse(
        data: List<User>.from(json["data"].map((x) => User.fromJson(x))),
        total: json["total"],
        page: json["page"],
        pageSize: json["pageSize"],
        totalPages: json["totalPages"],
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
        "page": page,
        "pageSize": pageSize,
        "totalPages": totalPages,
      };
}
