// To parse this JSON data, do
//
//     final staffDetailsResponse = staffDetailsResponseFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/user.dart';
import 'package:vegmove_warehouse/domain/model/warehouse.dart';

StaffDetailsResponse staffDetailsResponseFromJson(String str) =>
    StaffDetailsResponse.fromJson(json.decode(str));

class StaffDetailsResponse {
  final List<Warehouse> warehouses;
  final User user;

  StaffDetailsResponse({
    required this.warehouses,
    required this.user,
  });

  StaffDetailsResponse copyWith({
    List<Warehouse>? warehouses,
    User? user,
  }) =>
      StaffDetailsResponse(
        warehouses: warehouses ?? this.warehouses,
        user: user ?? this.user,
      );

  factory StaffDetailsResponse.fromJson(Map<String, dynamic> json) =>
      StaffDetailsResponse(
        warehouses: List<Warehouse>.from(
            json["warehouses"].map((x) => Warehouse.fromJson(x))),
        user: User.fromJson(json["user"]),
      );
}
