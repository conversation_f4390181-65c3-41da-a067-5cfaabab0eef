// To parse this JSON data, do
//
//     final order = orderFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_delivery/domain/model/order.dart';

OrdersResponse ordersResponseFromJson(String str) =>
    OrdersResponse.fromJson(json.decode(str));

String orderToJson(OrdersResponse data) => json.encode(data.toJson());

class OrdersResponse {
  final List<Order> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  OrdersResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  OrdersResponse copyWith({
    List<Order>? data,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      OrdersResponse(
        data: data ?? this.data,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory OrdersResponse.fromJson(Map<String, dynamic> json) => OrdersResponse(
        data: List<Order>.from(json['data'].map((x) => Order.fromJson(x))),
        total: json['total'],
        page: json['page'],
        pageSize: json['pageSize'],
        totalPages: json['totalPages'],
      );

  Map<String, dynamic> toJson() => {
        'data': List<dynamic>.from(data.map((x) => x.toJson())),
        'total': total,
        'page': page,
        'pageSize': pageSize,
        'totalPages': totalPages,
      };
}
