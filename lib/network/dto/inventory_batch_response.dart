import 'dart:convert';

import 'package:vegmove_warehouse/domain/model/inventory_batch.dart';

InventoryBatchResponse inventoryBatchResponseFromJson(String str) =>
    InventoryBatchResponse.fromJson(json.decode(str));

class InventoryBatchResponse {
  final List<InventoryBatch> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  InventoryBatchResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  InventoryBatchResponse copyWith({
    List<InventoryBatch>? data,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      InventoryBatchResponse(
        data: data ?? this.data,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory InventoryBatchResponse.from<PERSON>son(Map<String, dynamic> json) =>
      InventoryBatchResponse(
        data: List<InventoryBatch>.from(
            json["data"].map((x) => InventoryBatch.fromJson(x))),
        total: json["total"],
        page: json["page"],
        pageSize: json["pageSize"],
        totalPages: json["totalPages"],
      );
}
