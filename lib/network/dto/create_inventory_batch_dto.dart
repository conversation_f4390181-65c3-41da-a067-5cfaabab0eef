class CreateInventoryBatchDto {
  String batchName;
  int warehouseId;
  List<CreateInventoryItemDto> inventories;

  CreateInventoryBatchDto({
    required this.batchName,
    required this.warehouseId,
    required this.inventories,
  });

  Map<String, dynamic> toJson() {
    return {
      'batchName': batchName,
      'warehouseId': warehouseId,
      'inventories': inventories.map((item) => item.toJson()).toList(),
    };
  }
}

class CreateInventoryItemDto {
  String barcode;
  double buyingPrice;
  double sellingPrice;
  DateTime? manufactureDate;
  DateTime expiryDate;
  int? supplierId;
  int initialStock;

  CreateInventoryItemDto({
    required this.barcode,
    required this.buyingPrice,
    required this.sellingPrice,
    this.manufactureDate,
    required this.expiryDate,
    this.supplierId,
    required this.initialStock,
  });

  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'buyingPrice': buyingPrice,
      'sellingPrice': sellingPrice,
      'manufactureDate': manufactureDate?.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'supplierId': supplierId,
      'initialStock': initialStock,
    };
  }
}
