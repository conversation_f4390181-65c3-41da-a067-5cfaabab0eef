import 'package:vegmove_warehouse/domain/model/enums.dart';

class UpdateOrderStatusDto {
  final OrderStatus? orderStatus;
  final String? paymentStatus;
  final int? deliveryDriverId;

  UpdateOrderStatusDto({
    this.orderStatus,
    this.paymentStatus,
    this.deliveryDriverId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (orderStatus != null) {
      data['orderStatus'] = orderStatus!.name;
    }

    if (paymentStatus != null) {
      data['paymentStatus'] = paymentStatus;
    }

    if (deliveryDriverId != null) {
      data['deliveryDriverId'] = deliveryDriverId;
    }

    return data;
  }
}
