import 'dart:async';
import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_delivery/domain/model/delivery_driver_location.dart';
import 'package:vegmove_delivery/services/location_service.dart';

// Configuration provider for location tracking intervals
final locationTrackingConfigProvider = Provider<LocationTrackingConfig>((ref) {
  return LocationTrackingConfig(
    normalIntervalSeconds: 30, // 30 seconds when driver is logged in
    shippingIntervalSeconds: 10, // 10 seconds when there's a SHIPPED order
  );
});

// Provider for the location tracker
final locationTrackerProvider =
    StateNotifierProvider<LocationTrackerNotifier, LocationTrackerState>((ref) {
  final config = ref.watch(locationTrackingConfigProvider);
  return LocationTrackerNotifier(
    locationService: LocationService(),
    config: config,
  );
});

// Configuration class for location tracking
class LocationTrackingConfig {
  final int normalIntervalSeconds;
  final int shippingIntervalSeconds;

  LocationTrackingConfig({
    required this.normalIntervalSeconds,
    required this.shippingIntervalSeconds,
  });
}

// State class for location tracking
class LocationTrackerState {
  final bool isTracking;
  final bool hasActiveShippingOrder;
  final Position? lastPosition;
  final DeliveryDriverLocation? lastUpdatedLocation;
  final String? error;
  final bool isPermissionGranted;

  LocationTrackerState({
    this.isTracking = false,
    this.hasActiveShippingOrder = false,
    this.lastPosition,
    this.lastUpdatedLocation,
    this.error,
    this.isPermissionGranted = false,
  });

  LocationTrackerState copyWith({
    bool? isTracking,
    bool? hasActiveShippingOrder,
    Position? lastPosition,
    DeliveryDriverLocation? lastUpdatedLocation,
    String? error,
    bool? isPermissionGranted,
  }) {
    return LocationTrackerState(
      isTracking: isTracking ?? this.isTracking,
      hasActiveShippingOrder:
          hasActiveShippingOrder ?? this.hasActiveShippingOrder,
      lastPosition: lastPosition ?? this.lastPosition,
      lastUpdatedLocation: lastUpdatedLocation ?? this.lastUpdatedLocation,
      error: error,
      isPermissionGranted: isPermissionGranted ?? this.isPermissionGranted,
    );
  }
}

// Notifier class for location tracking
class LocationTrackerNotifier extends StateNotifier<LocationTrackerState> {
  final LocationService _locationService;
  final LocationTrackingConfig _config;
  Timer? _locationTimer;

  LocationTrackerNotifier({
    required LocationService locationService,
    required LocationTrackingConfig config,
  })  : _locationService = locationService,
        _config = config,
        super(LocationTrackerState()) {
    // Initialize location permissions
    _checkLocationPermission();
  }

  // Check if location permissions are granted
  Future<void> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        state = state.copyWith(
          error:
              'Location services are disabled. Please enable location services.',
          isPermissionGranted: false,
        );
        return;
      }

      // Check for location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          state = state.copyWith(
            error:
                'Location permissions are denied. Please enable location permissions.',
            isPermissionGranted: false,
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        state = state.copyWith(
          error:
              'Location permissions are permanently denied. Please enable location permissions in settings.',
          isPermissionGranted: false,
        );
        return;
      }

      // Permissions are granted
      state = state.copyWith(
        isPermissionGranted: true,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Error checking location permissions: $e',
        isPermissionGranted: false,
      );
    }
  }

  // Start tracking location
  Future<void> startTracking({bool hasShippingOrder = false}) async {
    if (!state.isPermissionGranted) {
      await _checkLocationPermission();
      if (!state.isPermissionGranted) {
        return;
      }
    }

    // Update state with shipping order status
    state = state.copyWith(
      isTracking: true,
      hasActiveShippingOrder: hasShippingOrder,
    );

    // Cancel existing timer if any
    _locationTimer?.cancel();

    // Determine update interval based on shipping status
    final intervalSeconds = hasShippingOrder
        ? _config.shippingIntervalSeconds
        : _config.normalIntervalSeconds;

    // Get current location immediately
    await _updateLocation();

    // Set up timer for periodic updates
    _locationTimer = Timer.periodic(
      Duration(seconds: intervalSeconds),
      (_) => _updateLocation(),
    );
  }

  // Stop tracking location
  void stopTracking() {
    _locationTimer?.cancel();
    _locationTimer = null;
    state = state.copyWith(isTracking: false);
  }

  // Update shipping order status
  void updateShippingOrderStatus(bool hasShippingOrder) {
    if (state.hasActiveShippingOrder != hasShippingOrder) {
      state = state.copyWith(hasActiveShippingOrder: hasShippingOrder);

      // Restart tracking with new interval if already tracking
      if (state.isTracking) {
        startTracking(hasShippingOrder: hasShippingOrder);
      }
    }
  }

  // Update location and send to server
  Future<void> _updateLocation() async {
    try {
      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Update state with new position
      state = state.copyWith(lastPosition: position);

      // Send location to server
      final location = await _locationService.updateLocation(
        lat: position.latitude.toString(),
        long: position.longitude.toString(),
      );

      if (location != null) {
        state = state.copyWith(lastUpdatedLocation: location);

        // Save last location to shared preferences
        _saveLocationToPrefs(position);
      }
    } catch (e) {
      log('Error updating location: $e');
      state = state.copyWith(error: 'Error updating location: $e');
    }
  }

  // Save location to shared preferences
  Future<void> _saveLocationToPrefs(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_location_lat', position.latitude);
      await prefs.setDouble('last_location_lng', position.longitude);
      await prefs.setString(
          'last_location_time', DateTime.now().toIso8601String());
    } catch (e) {
      log('Error saving location to prefs: $e');
    }
  }

  // Request location permission
  Future<void> requestLocationPermission() async {
    await _checkLocationPermission();
  }

  // Force a location update
  Future<void> forceLocationUpdate() async {
    await _updateLocation();
  }

  @override
  void dispose() {
    _locationTimer?.cancel();
    super.dispose();
  }
}
