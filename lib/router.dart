import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/ui/screens/address/address_form_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/address/saved_address_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/login_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/otp_verification_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/register_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/category/categories_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_category_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_details_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/home/<USER>';
import 'package:vegmove_ecommerce/ui/screens/home/<USER>';
import 'package:vegmove_ecommerce/ui/screens/home/<USER>';
import 'package:vegmove_ecommerce/ui/screens/location_picker/location_picker_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/my_bag_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/orders/order_details_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/orders/order_list_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/search/search_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/rewards/reward_history_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/rewards/reward_tiers_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/language_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/profile_settings_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/settings_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/update_email_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/update_name_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/settings/update_password_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/splash/splash_screen.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'root');
final GlobalKey<NavigatorState> _shellNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'shell');

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/',
    routes: [
      // Splash and Auth routes
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Auth routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/otp-verification',
        name: 'otp-verification',
        builder: (context, state) => const OtpVerificationScreen(),
      ),

      // Location picker route
      GoRoute(
        path: '/location-picker',
        name: 'location-picker',
        builder: (context, state) {
          // Check if a callback was provided (from VegmoveAppBar)
          final callback = state.extra as Function(LocationPickerResult)?;
          return LocationPickerScreen(
            onLocationSelected: callback,
          );
        },
      ),
      GoRoute(
        path: '/zone-checker',
        name: 'zone-checker',
        builder: (context, state) => LocationPickerScreen(
          onLocationSelected: (result) async {
            final prefs = await SharedPreferences.getInstance();
            if (result.isWithinZone) {
              // Save zone status
              prefs.setBool('isWithinZone', result.isWithinZone);

              // Save location data
              prefs.setDouble(
                  'selected_location_lat', result.location.latitude);
              prefs.setDouble(
                  'selected_location_lng', result.location.longitude);
              prefs.setString('selected_location_address', result.address);

              if (context.mounted) {
                context.goNamed('home');
              }
            } else {}
          },
        ),
      ),

      // Address routes
      GoRoute(
        path: '/saved-address',
        name: 'saved-address',
        builder: (context, state) => const SavedAddressScreen(),
      ),
      GoRoute(
        path: '/add-address',
        name: 'add-address',
        builder: (context, state) => const AddressFormScreen(),
      ),
      GoRoute(
        path: '/edit-address/:addressId',
        name: 'edit-address',
        builder: (context, state) {
          final addressId = int.parse(state.pathParameters['addressId']!);
          return AddressFormScreen(addressId: addressId);
        },
      ),

      // Order routes
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const OrderListScreen(),
      ),
      GoRoute(
        path: '/order-details/:orderId',
        name: 'order-details',
        builder: (context, state) {
          final orderId = int.parse(state.pathParameters['orderId']!);
          return OrderDetailsScreen(orderId: orderId);
        },
      ),

      // Language route
      GoRoute(
        path: '/language',
        name: 'language',
        builder: (context, state) => const LanguageScreen(),
      ),

      // Profile settings routes
      GoRoute(
        path: '/profile-settings',
        name: 'profile-settings',
        builder: (context, state) => const ProfileSettingsScreen(),
      ),
      GoRoute(
        path: '/update-name',
        name: 'update-name',
        builder: (context, state) => const UpdateNameScreen(),
      ),
      GoRoute(
        path: '/update-email',
        name: 'update-email',
        builder: (context, state) => const UpdateEmailScreen(),
      ),
      GoRoute(
        path: '/update-password',
        name: 'update-password',
        builder: (context, state) => const UpdatePasswordScreen(),
      ),

      // Reward routes
      GoRoute(
        path: '/reward-history',
        name: 'reward-history',
        builder: (context, state) => const RewardHistoryScreen(),
      ),
      GoRoute(
        path: '/reward-tiers',
        name: 'reward-tiers',
        builder: (context, state) => const RewardTiersScreen(),
      ),

      // Main app shell with bottom navigation
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) => NavigationScreen(child: child),
        // Use custom page builder for smoother transitions between tabs
        pageBuilder: (context, state, child) {
          return NoTransitionPage(
            key: state.pageKey,
            child: NavigationScreen(child: child),
          );
        },
        routes: [
          // Home tab and nested routes
          GoRoute(
            path: '/home',
            name: 'home',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                key: state.pageKey,
                child: const HomeScreen(),
              );
            },
            routes: [
              GoRoute(
                path: 'product/:productId/:slug',
                name: 'product-details',
                pageBuilder: (context, state) {
                  final productId = state.pathParameters['productId']!;
                  final slug = state.pathParameters['slug']!;
                  return CustomTransitionPage(
                    key: state.pageKey,
                    child: ProductDetailsScreen(
                      slug: slug,
                      productId: productId,
                    ),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    transitionDuration: const Duration(milliseconds: 150),
                  );
                },
              ),
              GoRoute(
                path: 'section/:sectionId',
                name: 'section-details',
                pageBuilder: (context, state) {
                  final sectionId = state.pathParameters['sectionId']!;
                  return CustomTransitionPage(
                    key: state.pageKey,
                    child: SectionDetailsScreen(sectionId: sectionId),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    transitionDuration: const Duration(milliseconds: 150),
                  );
                },
              ),
              GoRoute(
                path: 'category/:categoryId',
                name: 'category-details',
                pageBuilder: (context, state) {
                  final categoryId =
                      int.parse(state.pathParameters['categoryId']!);
                  return CustomTransitionPage(
                    key: state.pageKey,
                    child: CategoryDetailsScreen(categoryId: categoryId),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    transitionDuration: const Duration(milliseconds: 150),
                  );
                },
              ),
              GoRoute(
                path: 'categoryType',
                name: 'category-category-details',
                pageBuilder: (context, state) {
                  final category = (state.extra as Category);
                  return CustomTransitionPage(
                    key: state.pageKey,
                    child: CategoryCategoryScreen(
                      category: category,
                    ),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    transitionDuration: const Duration(milliseconds: 150),
                  );
                },
              ),
            ],
          ),

          // Categories tab
          GoRoute(
            path: '/categories',
            name: 'categories',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                key: state.pageKey,
                child: const CategoriesScreen(),
              );
            },
          ),

          // Search tab
          GoRoute(
            path: '/search',
            name: 'search',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                key: state.pageKey,
                child: const SearchScreen(),
              );
            },
          ),

          // My Bag tab
          GoRoute(
            path: '/my-bag',
            name: 'my-bag',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                key: state.pageKey,
                child: const MyBagScreen(),
              );
            },
          ),

          // Settings tab
          GoRoute(
            path: '/settings',
            name: 'settings',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                key: state.pageKey,
                child: const SettingsScreen(),
              );
            },
          ),
        ],
      ),
    ],
  );
});
